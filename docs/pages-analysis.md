# 页面功能分析文档

## 1. 充值页面 (Deposit)

**路由**: `/#/pages/deposit/add/add` → `src/views/recharge`

### 功能特性

- **当前余额显示**: 显示用户当前余额 (₹30.00)
- **金额输入**: 支持手动输入和预设金额选择
- **预设金额**: 10, 50, 100, 300, 1000, 3000, 5000, 10000
- **支付方式选择**: AliPay (+500 奖励) 和 UPI (+3.00%奖励)
- **支付渠道**: 根据支付方式动态变化
- **实时计算**: 现金余额和奖励金额实时更新
- **限额提示**: 不同支付方式有不同的最小/最大限额

### API 接口

1. **获取余额**: `GET /user/balance?cat=2&coin=5000&paypop=0`
2. **提交订单**: `POST /order/pay`

### 业务逻辑

- **支付方式配置**:
  - AliPay: 最小 ₹100, 最大 ₹1000000, 固定 ₹500 奖励 (`discoin: 500`)
  - UPI: 最小 ₹200, 最大 ₹100000, 3%奖励 (`rate: 0.03`)
- **动态计算逻辑**:
  - 奖励计算: `discoin > 0 ? discoin : amount * rate`
  - 限额显示: 根据选择的支付方式动态更新
  - 支付渠道: AliPay→"test1009", UPI→"Paytm APP"
- **验证规则**:
  - 金额范围验证: `mincoin <= amount <= maxcoin`
  - 错误提示: "Deposit amount cannot less than {mincoin}"
- **状态管理**:
  - 支付方式不可用: 显示错误码(3)的提示信息
  - 服务器错误: 500 状态码处理

### UI 元素

- 金额输入框
- 预设金额按钮网格
- 支付方式选择卡片
- 支付渠道显示
- 余额和奖励显示区域
- 存款按钮
- 提示信息区域

---

## 2. 提现页面 (Withdraw)

**路由**: `/#/pages/withdraw/index` → `src/views/withdraw`

### 功能特性

- **可提现余额显示**: 显示可提现金额 (₹0)
- **银行账户管理**: 添加新银行账户功能
- **金额输入**: 支持手动输入提现金额
- **限额显示**: 最小 ₹0, 最大 ₹1000000

### API 接口

1. **获取提现信息**: `GET /draw/index?drawpop=0`
2. **获取银行账户**: `GET /user/banks`

### 业务逻辑

- 需要先添加银行账户才能提现
- 提现金额验证
- 服务器连接状态检查

### UI 元素

- 可提现余额显示
- 添加银行账户卡片
- 金额输入区域
- 提现按钮
- 提示信息

---

## 3. 交易记录页面 (Transactions)

**路由**: `/#/pages/transaction/transaction` → `src/views/transactions`

### 功能特性

- **四个标签页**: Deposits, Withdrawals, Bet, Bonus
- **交易记录列表**: 按日期分组显示
- **交易详情**: ID、支付方式、时间、状态、金额
- **复制功能**: 交易 ID 可复制
- **客服联系**: 部分交易提供客服联系选项
- **状态筛选**: Success, In-process, Failed 等状态筛选

### API 接口

1. **获取交易历史**: `GET /user/history?num=1&size=10`

### 业务逻辑

- **数据结构**: 按日期分组 (`"08/05/2025": [...]`)
- **状态映射**:
  - 存款: `0`=处理中, `1`=成功, `2`=失败
  - 奖励: `34`=转出到现金余额, `35`=登录奖励
- **标签页数据**:
  - `deplist`: 存款记录 (有数据)
  - `drawlist`: 提现记录 (空数组)
  - `betlist`: 投注记录 (空数组)
  - `bonuslist`: 奖励记录 (有数据)
- **空状态处理**: 显示"You've not done any transactions till now"
- **客服联系**: 部分交易显示"Need help with this order?"

### UI 元素

- 标签页导航
- 状态筛选器
- 交易记录卡片
- 日期分组
- 复制按钮
- 客服联系按钮
- 空状态页面

---

## 4. 支付管理页面 (Manage Payments)

**路由**: `/#/pages/deposit/managepayment/managepayment` → `src/views/payments`

### 功能特性

- **银行账户管理**: 查看和添加银行账户
- **钱包管理**: 管理各种 UPI 钱包
- **支持的钱包**: Paytm, Phonepe, Gpay, Other UPI ID
- **链接状态**: 显示已链接或待链接状态

### API 接口

1. **获取支付方式**: `GET /user/banks?split=1`

### 业务逻辑

- 银行账户验证流程
- UPI 钱包绑定/解绑
- 支付方式状态管理

### UI 元素

- 银行账户卡片
- UPI 钱包列表
- 添加/链接按钮
- 状态指示器

---

## 5. 添加银行卡页面 (Add Bank Account)

**路由**: `/#/pages/deposit/addcreditcard/addcreditcard` → `src/views/withdraw/chanels/add`

### 功能特性

- **账户信息输入**: 账户号码、确认账户号码、IFSC 代码
- **用户信息**: 姓名字段（预填充且禁用）
- **文件上传**: 银行证明文件上传
- **表单验证**: 账户号码匹配验证、IFSC 格式验证
- **重要提示**: 账户添加后不可更改的警告

### API 接口

- 提交银行账户信息的 API（需要进一步分析）

### 业务逻辑

- 双重账户号码验证
- IFSC 代码格式验证
- 文件上传处理
- 一次性提交限制

### UI 元素

- 账户号码输入框
- 确认账户号码输入框
- IFSC 代码输入框
- 姓名显示字段
- 文件上传区域
- 重要提示框
- 提交按钮

---

## 通用特性

### 货币符号

- 所有页面使用 ₹ 符号
- 需要改为可配置的货币符号

### 错误处理

- 网络连接错误提示
- 表单验证错误提示
- 服务器错误处理

### 加载状态

- API 请求时的加载状态
- 页面切换时的过渡效果

### 响应式设计

- 移动端优先设计
- 适配不同屏幕尺寸

### 多语言支持需求

- 所有文本内容需要国际化
- 支持中文、英语、法语、西班牙语、葡萄牙语
- 考虑不同语言的文本长度影响
