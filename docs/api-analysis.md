# API 接口分析文档

**基础 URL**: `http://service.haiwailaba.cyou`

## 1. 用户余额接口

### GET /user/balance

**用途**: 获取用户余额信息和支付配置

**参数**:

- `cat`: 类别 (例: 2)
- `coin`: 金额 (例: 300) - 影响奖励计算
- `paypop`: 弹窗标识 (例: 0)

**示例请求**:

```
GET /user/balance?cat=2&coin=300&paypop=0
```

**响应结构**:

```json
{
  "code": 0,
  "msg": "succ",
  "data": {
    "user": {
      "uid": 10002,
      "coin": 30, // 当前余额
      "dcoin": 0, // 可提现余额
      "ecoin": 30, // 有效余额
      "bonus": 0, // 奖励余额
      "totalcoin": 30, // 总余额
      "kyc": 1, // KYC状态 (1=已验证)
      "svip": 0, // VIP状态
      "ispayer": 0, // 付费用户状态
      "nodislabelid": 0 // 标签ID
    },
    "coins": [10, 50, 100, 300, 1000, 3000, 5000, 10000], // 预设金额
    "channels": [
      {
        "id": 10,
        "title": "AliPay",
        "icon": "https://img.yonogames.com//upload/********/9e2244e45601065efb0a9247cf29f689.png",
        "subtype": "onlinepay",
        "mincoin": 100, // 最小金额
        "maxcoin": 1000000, // 最大金额
        "disrate": "+500", // 显示费率
        "discoin": 500, // 固定奖励金额
        "type": 0,
        "pages": [
          {
            "id": 10,
            "title": "test1009",
            "type": 1,
            "banktype": 0,
            "mincoin": 100,
            "maxcoin": 1000000,
            "disrate": "+500",
            "discoin": 500,
            "rate": "1.67" // 实际费率
          }
        ]
      },
      {
        "id": 17,
        "title": "upi",
        "mincoin": 200,
        "maxcoin": 100000,
        "disrate": "+3.00%",
        "discoin": 0, // 0表示按百分比计算
        "pages": [
          {
            "id": 16,
            "title": "Paytm APP",
            "rate": 0.03 // 3%费率
          }
        ]
      }
    ],
    "memo": "Tips: aaaa<br/>\nbbbbb<br/>\nccccc", // 提示信息
    "popmsg": "", // 弹窗消息
    "customer": [], // 客服信息
    "url": "https://vm.providesupport.com/04qetijpc30hp11ayhbj2xfvh4" // 客服URL
  }
}
```

**业务逻辑**:

- `coin`参数影响奖励计算显示
- `discoin`为 0 时按`rate`百分比计算，否则为固定金额
- 不同支付方式有不同的限额和费率

---

## 2. 订单支付接口

### POST /order/pay

**用途**: 提交充值订单

**请求方法**: POST
**Content-Type**: application/json

**请求参数** (推测):

```json
{
  "amount": 300,
  "payment_method": "alipay", // 或 "upi"
  "channel": "test1009" // 或 "Paytm APP"
}
```

**响应处理**:

- 成功: 返回订单信息或跳转 URL
- 失败: 返回错误信息 (如支付方式不可用)

---

## 3. 提现相关接口

### GET /draw/index

**用途**: 获取提现页面信息

**参数**:

- `drawpop`: 弹窗标识 (例: 0)

**示例请求**:

```
GET /draw/index?drawpop=0
```

**响应结构**:

```json
{
  "code": 0,
  "msg": "succ",
  "data": {
    "uid": 10002,
    "dcoin": 0, // 可提现余额
    "limit": 0, // 提现限制
    "mincoin": 0, // 最小提现金额
    "maxcoin": 1000000, // 最大提现金额
    "memo": "bbbbdddd<br/>awerqwerqwer", // 提示信息
    "popmsg": "", // 弹窗消息
    "customer": [], // 客服信息
    "url": "https://vm.providesupport.com/04qetijpc30hp11ayhbj2xfvh4"
  }
}
```

---

## 4. 银行账户接口

### GET /user/banks

**用途**: 获取用户银行账户信息

**参数**:

- `split`: 分割标识 (可选, 例: 1)

**示例请求**:

```
GET /user/banks
GET /user/banks?split=1
```

**响应结构**:

```json
{
  "code": 0,
  "msg": "succ",
  "data": {
    "banks": [], // 银行账户列表 (空表示无账户)
    "upis": {
      // UPI钱包信息 (仅当split=1时返回)
      "1": {
        "id": 1,
        "name": "Paytm",
        "checked": false, // false=已绑定, true=未绑定
        "card": "asdasd", // 绑定的账户ID
        "icon": "/static/img/paytm.png",
        "cat": 1 // 钱包类别
      },
      "2": {
        "id": 1,
        "name": "Phonepe",
        "checked": true, // true=未绑定
        "card": "Link", // "Link"表示待绑定
        "icon": "/static/img/phonepe.png",
        "cat": 2
      }
    },
    "kyc": 1, // KYC验证状态
    "url": "https://vm.providesupport.com/04qetijpc30hp11ayhbj2xfvh4"
  }
}
```

**业务逻辑**:

- `split=1`时返回 UPI 钱包详细信息
- `checked: false`表示已绑定，`checked: true`表示未绑定
- `card: "Link"`表示待绑定状态

---

## 5. 交易历史接口

### GET /user/history

**用途**: 获取用户交易历史记录

**参数**:

- `num`: 页码 (例: 1)
- `size`: 每页数量 (例: 10)

**示例请求**:

```
GET /user/history?num=1&size=10
```

**响应结构**:

```json
{
  "code": 0,
  "msg": "succ",
  "data": {
    "deplist": {
      // 存款记录，按日期分组
      "08/05/2025": [
        {
          "id": 14,
          "orderid": "202508051827350110256989",
          "coin": "300.00",
          "status_str": "In-Process",
          "status": 1, // 状态码: 0=处理中, 1=成功, 2=失败
          "time": "06:27 pm",
          "title": "upi", // 支付方式
          "memo": "" // 备注信息
        }
      ]
    },
    "drawlist": [], // 提现记录 (空数组表示无记录)
    "betlist": [], // 投注记录 (空数组表示无记录)
    "bonuslist": {
      // 奖励记录，按日期分组
      "08/05/2025": [
        {
          "id": 6,
          "orderid": "2025080511552713959254",
          "coin": "-20.00", // 负数表示转出
          "title": "签到彩金，第2天",
          "status": 34, // 奖励状态码
          "time": "05:25 pm",
          "status_str": "Transfer to Cash Balance"
        },
        {
          "id": 5,
          "orderid": "2025080511552713959254",
          "coin": "20.00", // 正数表示获得
          "title": "签到彩金，第2天",
          "status": 35,
          "time": "05:25 pm",
          "status_str": "Login bonus"
        }
      ]
    },
    "banklist": [], // 银行相关记录
    "show": 1, // 显示标识
    "url": "https://vm.providesupport.com/04qetijpc30hp11ayhbj2xfvh4"
  }
}
```

**状态码映射**:

- 存款状态: `0`=处理中, `1`=成功, `2`=失败
- 奖励状态: `34`=转出到现金余额, `35`=登录奖励

**业务逻辑**:

- 数据按日期分组，格式为 "MM/DD/YYYY"
- 空数组表示该类型无交易记录
- 奖励记录中负数表示转出，正数表示获得

---

## 6. 银行卡添加接口 (推测)

### POST /user/banks (推测)

**用途**: 添加新的银行账户

**请求参数** (推测):

```json
{
  "account_number": "**********",
  "confirm_account_number": "**********",
  "ifsc_code": "ABCD0123456",
  "name": "Test User",
  "bank_proof": "base64_encoded_file_data"
}
```

**验证规则**:

- 账户号码必须匹配
- IFSC 代码格式验证 (11 位)
- 银行证明文件必须上传

---

## 错误处理

### 常见错误响应

1. **金额验证错误**:

   ```
   "Deposit amount cannot less than 200"
   ```

2. **支付方式不可用**:

   ```
   "This payment method is not available now,Please choose other payment methods to pay(3)!"
   ```

3. **服务器连接错误**:

   ```
   "无法连接到服务器~"
   ```

4. **500 内部服务器错误**:
   - 服务器处理异常
   - 需要重试或联系客服

---

## 请求头设置

**推荐请求头**:

```
Content-Type: application/json
Accept: application/json
User-Agent: Mozilla/5.0 (compatible; PaySMS App)
```

**认证** (推测):

- 可能需要 Token 或 Session 认证
- 需要进一步分析认证机制

---

## 响应格式 (推测)

**成功响应**:

```json
{
  "code": 0,
  "msg": "success",
  "data": {
    // 具体数据
  }
}
```

**错误响应**:

```json
{
  "code": 1,
  "msg": "error message",
  "data": null
}
```

---

## 需要进一步分析的接口

1. **用户认证接口**: 登录/注册相关
2. **文件上传接口**: 银行证明上传
3. **UPI 钱包绑定接口**: 钱包链接/解绑
4. **订单状态查询接口**: 实时订单状态
5. **客服联系接口**: 客服系统集成

---

## 安全考虑

1. **HTTPS**: 生产环境应使用 HTTPS
2. **参数验证**: 所有输入参数需要验证
3. **敏感信息**: 银行账户信息需要加密传输
4. **频率限制**: API 调用频率限制
5. **错误信息**: 避免泄露敏感系统信息
