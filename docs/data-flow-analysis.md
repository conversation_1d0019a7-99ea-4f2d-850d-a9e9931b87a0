# 数据流与业务逻辑分析文档

## 1. 充值页面数据流分析

### 页面加载流程
1. **初始化请求**: `GET /user/balance?cat=2&coin=300&paypop=0`
2. **数据处理**: 解析响应数据并更新UI状态
3. **UI渲染**: 根据数据渲染支付方式、预设金额等

### 关键数据映射

#### 用户余额显示
```javascript
// API响应 → UI显示
data.user.coin → "Current Balance: ₹30.00"
```

#### 预设金额按钮
```javascript
// API响应 → UI渲染
data.coins → [10, 50, 100, 300, 1000, 3000, 5000, 10000]
// 渲染为按钮网格
```

#### 支付方式动态切换
```javascript
// 选择AliPay时
selectedChannel = data.channels[0] // id: 10
limitText = `₹${selectedChannel.mincoin} & max:₹${selectedChannel.maxcoin}`
bonusAmount = selectedChannel.discoin // 固定500

// 选择UPI时  
selectedChannel = data.channels[1] // id: 17
limitText = `₹${selectedChannel.mincoin} & max:₹${selectedChannel.maxcoin}`
bonusAmount = inputAmount * selectedChannel.pages[0].rate // 3%计算
```

#### 奖励计算逻辑
```javascript
function calculateBonus(amount, channel) {
  if (channel.discoin > 0) {
    // 固定奖励 (AliPay)
    return channel.discoin; // 500
  } else {
    // 百分比奖励 (UPI)
    return amount * channel.pages[0].rate; // amount * 0.03
  }
}
```

### 表单验证逻辑
```javascript
function validateAmount(amount, channel) {
  if (amount < channel.mincoin) {
    showError(`Deposit amount cannot less than ${channel.mincoin}`);
    return false;
  }
  if (amount > channel.maxcoin) {
    showError(`Deposit amount cannot exceed ${channel.maxcoin}`);
    return false;
  }
  return true;
}
```

### 订单提交流程
1. **验证金额**: 检查是否符合当前支付方式的限额
2. **构建请求**: 组装订单数据
3. **发送请求**: `POST /order/pay`
4. **处理响应**: 根据响应结果显示成功/错误信息

---

## 2. 交易记录页面数据流分析

### 数据加载与分组
```javascript
// API响应处理
function processTransactionData(response) {
  const { deplist, drawlist, betlist, bonuslist } = response.data;
  
  // 按日期分组显示
  Object.keys(deplist).forEach(date => {
    renderDateGroup(date, deplist[date]);
  });
}
```

### 状态映射逻辑
```javascript
const statusMapping = {
  0: { text: 'In-Process', class: 'status-pending' },
  1: { text: 'Success', class: 'status-success' },
  2: { text: 'Failed', class: 'status-failed' }
};

function renderTransactionStatus(status) {
  const mapping = statusMapping[status];
  return `<span class="${mapping.class}">${mapping.text}</span>`;
}
```

### 标签页切换逻辑
```javascript
function switchTab(tabType) {
  const dataMap = {
    'deposits': 'deplist',
    'withdrawals': 'drawlist', 
    'bet': 'betlist',
    'bonus': 'bonuslist'
  };
  
  const data = transactionData[dataMap[tabType]];
  renderTransactionList(data);
}
```

---

## 3. 提现页面数据流分析

### 页面初始化
```javascript
// 并行请求获取数据
Promise.all([
  fetch('/draw/index?drawpop=0'),
  fetch('/user/banks')
]).then(([drawData, bankData]) => {
  updateWithdrawBalance(drawData.data.dcoin);
  updateBankAccounts(bankData.data.banks);
  updateWithdrawLimits(drawData.data.mincoin, drawData.data.maxcoin);
});
```

### 银行账户状态检查
```javascript
function checkBankAccountStatus(banks) {
  if (banks.length === 0) {
    showAddBankAccountPrompt();
    disableWithdrawButton();
  } else {
    showBankAccountList(banks);
    enableWithdrawButton();
  }
}
```

---

## 4. 支付管理页面数据流分析

### UPI钱包状态渲染
```javascript
function renderUPIWallets(upis) {
  Object.values(upis).forEach(wallet => {
    const isLinked = !wallet.checked; // 注意：checked=false表示已绑定
    const displayText = isLinked ? wallet.card : 'Link';
    const buttonClass = isLinked ? 'linked' : 'unlinked';
    
    renderWalletItem(wallet.name, displayText, buttonClass, wallet.icon);
  });
}
```

### 绑定状态逻辑
```javascript
// 状态判断逻辑 (注意反向逻辑)
const walletStatus = {
  linked: wallet.checked === false,    // 已绑定
  unlinked: wallet.checked === true    // 未绑定
};
```

---

## 5. 前端计算逻辑

### 金额格式化
```javascript
function formatCurrency(amount) {
  return `₹${parseFloat(amount).toFixed(2)}`;
}
```

### 时间格式化
```javascript
function formatTime(timeStr) {
  // API返回格式: "06:27 pm"
  // 直接使用，无需转换
  return timeStr;
}
```

### 日期格式化
```javascript
function formatDate(dateStr) {
  // API返回格式: "08/05/2025"
  // 转换为显示格式
  return dateStr; // 保持原格式
}
```

---

## 6. 错误处理逻辑

### API错误处理
```javascript
function handleAPIError(error, context) {
  const errorMessages = {
    400: '请求参数错误',
    401: '未授权访问',
    403: '访问被拒绝', 
    404: '接口不存在',
    500: '服务器内部错误'
  };
  
  const message = errorMessages[error.status] || '网络连接失败';
  showErrorMessage(message, context);
}
```

### 业务错误处理
```javascript
function handleBusinessError(response) {
  if (response.code !== 0) {
    switch (response.code) {
      case 1001:
        showError('金额不符合要求');
        break;
      case 1002:
        showError('支付方式不可用');
        break;
      default:
        showError(response.msg || '操作失败');
    }
    return false;
  }
  return true;
}
```

---

## 7. 状态管理

### 页面状态
```javascript
const pageState = {
  loading: false,
  selectedPaymentMethod: null,
  selectedAmount: 0,
  userBalance: 0,
  transactionData: null,
  currentTab: 'deposits'
};
```

### 状态更新函数
```javascript
function updatePageState(key, value) {
  pageState[key] = value;
  
  // 触发相关UI更新
  switch (key) {
    case 'selectedPaymentMethod':
      updatePaymentMethodUI();
      break;
    case 'selectedAmount':
      updateBonusCalculation();
      break;
    case 'currentTab':
      switchTransactionTab(value);
      break;
  }
}
```

---

## 8. 数据验证规则

### 充值金额验证
```javascript
const validationRules = {
  amount: {
    required: true,
    min: (channel) => channel.mincoin,
    max: (channel) => channel.maxcoin,
    type: 'number'
  }
};
```

### 银行账户验证
```javascript
const bankAccountRules = {
  accountNumber: {
    required: true,
    pattern: /^\d{9,18}$/,
    message: '账户号码格式不正确'
  },
  ifscCode: {
    required: true,
    pattern: /^[A-Z]{4}0[A-Z0-9]{6}$/,
    length: 11,
    message: 'IFSC代码格式不正确'
  }
};
```

---

## 9. 缓存策略

### 数据缓存
```javascript
const cacheStrategy = {
  userBalance: { ttl: 30000 }, // 30秒
  paymentMethods: { ttl: 300000 }, // 5分钟
  transactionHistory: { ttl: 60000 }, // 1分钟
  bankAccounts: { ttl: 600000 } // 10分钟
};
```

### 缓存更新触发
- 用户操作后立即刷新相关缓存
- 页面获得焦点时检查缓存有效性
- 定时刷新关键数据
