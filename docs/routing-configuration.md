# 路由系统配置文档

## 概述

本项目实现了与原网站完全一致的路由结构，使用Vue Router 4和Hash模式，确保路由路径与原网站保持完全相同。

## 路由映射关系

| 原网站路径 | 新项目路径 | 组件路径 | 路由名称 | 说明 |
|-----------|-----------|----------|----------|------|
| `/#/pages/deposit/add/add` | `/pages/deposit/add/add` | `src/views/recharge/index.vue` | `DepositAdd` | 充值页面 |
| `/#/pages/withdraw/index` | `/pages/withdraw/index` | `src/views/withdraw/index.vue` | `WithdrawIndex` | 提现页面 |
| `/#/pages/transaction/transaction` | `/pages/transaction/transaction` | `src/views/transactions/index.vue` | `TransactionTransaction` | 交易记录页面 |
| `/#/pages/deposit/managepayment/managepayment` | `/pages/deposit/managepayment/managepayment` | `src/views/payments/index.vue` | `DepositManagePayment` | 支付管理页面 |
| `/#/pages/deposit/addcreditcard/addcreditcard` | `/pages/deposit/addcreditcard/addcreditcard` | `src/views/withdraw/chanels/add/index.vue` | `DepositAddCreditCard` | 添加银行卡页面 |

## 路由配置结构

### 基础配置
```typescript
// src/router/index.ts
import { createRouter, createWebHashHistory } from 'vue-router'

const router = createRouter({
  // 使用Hash模式保持与原网站一致
  history: createWebHashHistory(),
  routes: [...],
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})
```

### 路由层级结构
```
/
├── / (重定向到 /pages/deposit/add/add)
├── /pages (主布局)
│   ├── deposit/add/add (充值页面)
│   ├── withdraw/index (提现页面)
│   ├── transaction/transaction (交易记录)
│   ├── deposit/managepayment/managepayment (支付管理)
│   └── deposit/addcreditcard/addcreditcard (添加银行卡)
└── /:pathMatch(.*)*  (404页面)
```

## 路由元信息 (Meta)

每个路由都包含以下元信息：

```typescript
interface RouteMeta {
  title: string;           // 页面标题的i18n键
  requiresAuth: boolean;   // 是否需要身份验证
  keepAlive: boolean;      // 是否需要缓存组件
}
```

### 示例配置
```typescript
{
  path: 'deposit/add/add',
  name: 'DepositAdd',
  component: () => import('@/views/recharge/index.vue'),
  meta: {
    title: 'deposit.title',
    requiresAuth: true,
    keepAlive: false
  }
}
```

## 布局系统

### 主布局 (MainLayout.vue)
- **位置**: `src/layouts/MainLayout.vue`
- **功能**: 
  - 顶部导航栏
  - 移动端底部导航
  - 语言切换
  - 主题切换
  - 用户余额显示
  - 页面标题管理

### 布局特性
1. **响应式设计**: 桌面端顶部导航，移动端底部导航
2. **导航高亮**: 自动高亮当前活跃路由
3. **页面过渡**: 路由切换时的平滑过渡动画
4. **组件缓存**: 支持keep-alive缓存指定组件

## 导航配置

### 导航菜单项
```typescript
const navigationItems = [
  {
    name: 'deposit',
    path: '/pages/deposit/add/add',
    label: 'nav.deposit',
    icon: '💰'
  },
  {
    name: 'withdraw',
    path: '/pages/withdraw/index',
    label: 'nav.withdraw',
    icon: '💸'
  },
  {
    name: 'transactions',
    path: '/pages/transaction/transaction',
    label: 'nav.transactions',
    icon: '📊'
  },
  {
    name: 'payments',
    path: '/pages/deposit/managepayment/managepayment',
    label: 'nav.payments',
    icon: '💳'
  }
]
```

## 路由守卫

### 全局前置守卫
```typescript
router.beforeEach(async (to, from, next) => {
  // 1. 设置页面标题
  if (to.meta.title) {
    document.title = `PaySMS - ${String(to.meta.title)}`
  }
  
  // 2. 身份验证检查
  if (to.meta.requiresAuth) {
    // TODO: 实现身份验证逻辑
    // const isAuthenticated = await checkAuth()
    // if (!isAuthenticated) {
    //   next('/login')
    //   return
    // }
  }
  
  next()
})
```

### 全局后置钩子
```typescript
router.afterEach((to, from) => {
  // 路由切换完成后的处理
  console.log(`Route changed from ${from.path} to ${to.path}`)
})
```

## 路由常量

### 路由名称常量
```typescript
export const ROUTE_NAMES = {
  DEPOSIT_ADD: 'DepositAdd',
  WITHDRAW_INDEX: 'WithdrawIndex', 
  TRANSACTION_TRANSACTION: 'TransactionTransaction',
  DEPOSIT_MANAGE_PAYMENT: 'DepositManagePayment',
  DEPOSIT_ADD_CREDIT_CARD: 'DepositAddCreditCard',
  NOT_FOUND: 'NotFound'
} as const
```

### 路由路径常量
```typescript
export const ROUTE_PATHS = {
  DEPOSIT_ADD: '/pages/deposit/add/add',
  WITHDRAW_INDEX: '/pages/withdraw/index',
  TRANSACTION_TRANSACTION: '/pages/transaction/transaction', 
  DEPOSIT_MANAGE_PAYMENT: '/pages/deposit/managepayment/managepayment',
  DEPOSIT_ADD_CREDIT_CARD: '/pages/deposit/addcreditcard/addcreditcard'
} as const
```

## 导航工具函数

### 编程式导航
```typescript
export function useRouterNavigation() {
  return {
    goToDeposit: () => router.push(ROUTE_PATHS.DEPOSIT_ADD),
    goToWithdraw: () => router.push(ROUTE_PATHS.WITHDRAW_INDEX),
    goToTransactions: () => router.push(ROUTE_PATHS.TRANSACTION_TRANSACTION),
    goToPayments: () => router.push(ROUTE_PATHS.DEPOSIT_MANAGE_PAYMENT),
    goToAddBankAccount: () => router.push(ROUTE_PATHS.DEPOSIT_ADD_CREDIT_CARD)
  }
}
```

### 使用示例
```vue
<script setup>
import { useRouterNavigation } from '@/router'

const { goToDeposit, goToWithdraw } = useRouterNavigation()

function handleDepositClick() {
  goToDeposit()
}
</script>
```

## 页面组件结构

### 组件目录结构
```
src/views/
├── recharge/
│   └── index.vue              # 充值页面
├── withdraw/
│   ├── index.vue              # 提现页面
│   └── chanels/
│       └── add/
│           └── index.vue      # 添加银行卡页面
├── transactions/
│   └── index.vue              # 交易记录页面
├── payments/
│   └── index.vue              # 支付管理页面
└── error/
    └── 404.vue                # 404错误页面
```

### 页面组件模板
每个页面组件都包含：
1. **页面标题**: 使用i18n翻译
2. **主要内容**: 页面特定的功能区域
3. **路由信息**: 开发阶段显示路由调试信息
4. **响应式布局**: 适配不同屏幕尺寸

## 页面过渡动画

### CSS过渡类
```css
.page-transition-enter-active,
.page-transition-leave-active {
  transition: all 0.3s ease;
}

.page-transition-enter-from {
  opacity: 0;
  transform: translateX(20px);
}

.page-transition-leave-to {
  opacity: 0;
  transform: translateX(-20px);
}
```

### 路由视图配置
```vue
<router-view v-slot="{ Component, route }">
  <transition
    name="page-transition"
    mode="out-in"
    appear
  >
    <keep-alive :include="keepAliveRoutes">
      <component :is="Component" :key="route.path" />
    </keep-alive>
  </transition>
</router-view>
```

## 移动端适配

### 底部导航
- 在移动端显示底部标签栏导航
- 桌面端隐藏，使用顶部导航
- 自动高亮当前页面

### 响应式断点
```css
/* 移动端样式 */
@media (max-width: 768px) {
  .main-layout {
    padding-bottom: 80px; /* 为底部导航留出空间 */
  }
}
```

## 最佳实践

### 1. 路由命名
- 使用描述性的路由名称
- 保持与原网站路径的一致性
- 使用常量避免硬编码

### 2. 组件懒加载
- 所有页面组件使用动态导入
- 减少初始包大小
- 提高首屏加载速度

### 3. 路由元信息
- 合理使用meta字段
- 统一管理页面标题
- 实现权限控制

### 4. 错误处理
- 提供404页面
- 处理路由导航错误
- 友好的错误提示

这个路由系统确保了与原网站完全一致的URL结构，同时提供了现代化的单页应用体验。
