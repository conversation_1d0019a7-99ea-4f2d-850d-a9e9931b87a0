# 用户流程分析文档

## 1. 充值流程 (Deposit Flow)

### 主要流程

1. **进入充值页面**

   - 调用 `GET /user/balance?cat=2&coin=300&paypop=0`
   - 解析响应数据: `user.coin` → 当前余额显示
   - 渲染支付方式: `channels` → 支付选项卡片
   - 渲染预设金额: `coins` → 金额按钮网格

2. **选择充值金额**

   - 手动输入: 触发实时验证和计算
   - 预设金额点击: 更新输入框值，触发计算
   - 金额范围: 根据选中支付方式的 `mincoin/maxcoin`

3. **选择支付方式**

   - AliPay 选择:
     - 限额更新: `mincoin: 100, maxcoin: 1000000`
     - 奖励计算: `discoin: 500` (固定)
     - 渠道显示: `pages[0].title: "test1009"`
   - UPI 选择:
     - 限额更新: `mincoin: 200, maxcoin: 100000`
     - 奖励计算: `amount * rate: amount * 0.03`
     - 渠道显示: `pages[0].title: "Paytm APP"`

4. **实时计算显示**

   - 现金余额 = 输入金额 (直接显示)
   - 现金奖励 = `discoin > 0 ? discoin : amount * rate`
   - UI 更新: 限额文本、奖励金额同步更新

5. **提交订单**
   - 前端验证: `amount >= mincoin && amount <= maxcoin`
   - 构建请求数据: 包含金额、支付方式、渠道信息
   - 调用 `POST /order/pay`
   - 响应处理: 成功跳转或显示错误信息

### 错误处理流程

- **金额过低**: 显示最小金额要求提示
- **支付方式不可用**: 显示错误信息，建议选择其他方式
- **网络错误**: 显示连接失败提示
- **服务器错误**: 显示系统错误，建议稍后重试

### 成功流程

- 订单创建成功后跳转到支付页面或显示支付信息
- 更新用户余额
- 记录交易历史

---

## 2. 提现流程 (Withdraw Flow)

### 主要流程

1. **进入提现页面**

   - 显示可提现余额
   - 检查是否有绑定的银行账户

2. **银行账户检查**

   - 如果没有银行账户: 显示"添加新银行账户"选项
   - 如果有银行账户: 显示账户列表供选择

3. **添加银行账户流程** (如需要)

   - 跳转到添加银行卡页面
   - 填写账户信息
   - 上传银行证明
   - 提交审核

4. **输入提现金额**

   - 验证金额范围 (₹0-₹1000000)
   - 检查是否超过可提现余额

5. **提交提现申请**
   - 调用提现 API
   - 等待处理结果

### 前置条件

- 用户必须完成身份验证
- 必须有已验证的银行账户
- 有足够的可提现余额

---

## 3. 添加银行账户流程 (Add Bank Account Flow)

### 主要流程

1. **进入添加银行卡页面**

   - 显示表单字段
   - 预填充用户姓名 (禁用编辑)

2. **填写银行信息**

   - 输入银行账户号码
   - 重新输入账户号码确认
   - 输入 11 位 IFSC 代码

3. **实时验证**

   - 账户号码格式验证
   - 两次输入的账户号码必须一致
   - IFSC 代码格式验证 (11 位字母数字)

4. **上传银行证明**

   - 选择银行证明文件
   - 文件格式和大小验证
   - 预览上传的文件

5. **提交审核**
   - 显示重要提示: "银行账户一旦添加无法更改"
   - 用户确认后提交
   - 等待审核结果

### 验证规则

- 账户号码: 数字格式，长度验证
- IFSC 代码: 11 位，字母数字组合
- 银行证明: 支持的文件格式 (JPG, PNG, PDF)
- 文件大小限制

---

## 4. 交易记录查看流程 (Transaction History Flow)

### 主要流程

1. **进入交易记录页面**

   - 默认显示存款记录
   - 加载最近的交易数据

2. **标签页切换**

   - Deposits: 充值记录
   - Withdrawals: 提现记录
   - Bet: 投注记录
   - Bonus: 奖励记录

3. **筛选和查看**

   - 按状态筛选 (Success, In-process, Failed)
   - 按日期分组显示
   - 分页加载更多记录

4. **交互功能**
   - 复制交易 ID
   - 联系客服 (针对特定交易)
   - 查看交易详情

### 数据展示

- 交易 ID (可复制)
- 支付方式 (AliPay, UPI 等)
- 交易时间
- 交易状态
- 交易金额
- 客服联系选项 (如适用)

---

## 5. 支付管理流程 (Payment Management Flow)

### 主要流程

1. **进入支付管理页面**

   - 显示已绑定的银行账户
   - 显示 UPI 钱包绑定状态

2. **银行账户管理**

   - 查看已验证的银行账户
   - 添加新的银行账户
   - 账户状态查看

3. **UPI 钱包管理**

   - 查看各钱包绑定状态:
     - Paytm (已绑定显示 ID)
     - Phonepe (显示链接按钮)
     - Gpay (显示链接按钮)
     - Other UPI ID (显示链接按钮)

4. **绑定/解绑操作**
   - 点击"Link"绑定新钱包
   - 输入钱包 ID 或手机号
   - 验证绑定信息
   - 更新绑定状态

---

## 通用用户体验流程

### 加载状态

1. **页面加载**: 显示骨架屏或加载动画
2. **API 请求**: 显示加载指示器
3. **数据更新**: 平滑的状态转换

### 错误处理

1. **网络错误**: 显示重试按钮
2. **验证错误**: 实时显示错误信息
3. **系统错误**: 友好的错误页面

### 成功反馈

1. **操作成功**: Toast 提示或成功页面
2. **状态更新**: 实时更新相关数据
3. **导航引导**: 引导用户到下一步操作

### 响应式适配

1. **移动端**: 触摸友好的交互
2. **平板端**: 适配中等屏幕尺寸
3. **桌面端**: 充分利用大屏幕空间

### 多语言考虑

1. **文本长度**: 不同语言文本长度差异
2. **布局调整**: 适应不同语言的排版需求
3. **文化适应**: 考虑不同地区的使用习惯

---

## 关键用户路径

### 新用户首次充值

1. 注册/登录 → 2. 进入充值页面 → 3. 选择金额和支付方式 → 4. 完成支付 → 5. 查看交易记录

### 老用户提现

1. 登录 → 2. 进入提现页面 → 3. 选择银行账户 → 4. 输入金额 → 5. 提交申请 → 6. 等待处理

### 支付方式管理

1. 登录 → 2. 进入支付管理 → 3. 添加/管理银行账户 → 4. 绑定 UPI 钱包 → 5. 完成设置
