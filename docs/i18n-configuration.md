# 多语言国际化配置文档

## 概述

本项目实现了完整的多语言国际化支持，包含中文、英语、法语、西班牙语、葡萄牙语五种语言，并支持可配置的货币符号系统。

## 支持的语言

| 语言代码 | 语言名称 | 货币符号 | 状态 |
|---------|---------|---------|------|
| zh-CN   | 中文     | ¥       | ✅ 完成 |
| en-US   | English  | $       | ✅ 完成 |
| fr-FR   | Français | €       | ✅ 完成 |
| es-ES   | Español  | €       | ✅ 完成 |
| pt-BR   | Português| R$      | ✅ 完成 |

## 文件结构

```
src/
├── locales/                    # 语言文件目录
│   ├── zh-CN.ts               # 中文翻译
│   ├── en-US.ts               # 英文翻译
│   ├── fr-FR.ts               # 法文翻译
│   ├── es-ES.ts               # 西班牙文翻译
│   └── pt-BR.ts               # 葡萄牙文翻译
├── plugins/
│   └── i18n.ts                # i18n配置和工具函数
├── composables/
│   └── useI18n.ts             # i18n组合式函数
└── components/
    └── LanguageSwitcher.vue   # 语言切换组件
```

## 翻译键结构

### 通用翻译键
```typescript
common: {
  currency: string,      // 货币符号
  loading: string,       // 加载中
  submit: string,        // 提交
  cancel: string,        // 取消
  confirm: string,       // 确认
  // ... 更多通用键
}
```

### 页面特定翻译键
```typescript
// 充值页面
deposit: {
  title: string,
  currentBalance: string,
  amount: string,
  paymentMethod: string,
  errors: {
    amountTooLow: string,    // 支持参数插值: {min}
    amountTooHigh: string,   // 支持参数插值: {max}
    // ... 更多错误信息
  }
}

// 交易记录页面
transactions: {
  title: string,
  tabs: {
    deposits: string,
    withdrawals: string,
    bet: string,
    bonus: string
  },
  status: {
    success: string,
    inProcess: string,
    failed: string
  }
}
```

## 使用方法

### 1. 基础翻译

```vue
<template>
  <div>
    <h1>{{ t('deposit.title') }}</h1>
    <p>{{ t('deposit.currentBalance') }}: {{ formatCurrency(balance) }}</p>
  </div>
</template>

<script setup>
import { useI18n, useCurrency } from '@/composables/useI18n'

const { t } = useI18n()
const { format: formatCurrency } = useCurrency()
</script>
```

### 2. 参数插值

```vue
<template>
  <div>
    <!-- 错误信息插值 -->
    <p class="error">{{ t('deposit.errors.amountTooLow', { min: 100 }) }}</p>
    
    <!-- 多参数插值 -->
    <p>{{ t('deposit.allowedRange', { min: 100, max: 1000000 }) }}</p>
  </div>
</template>
```

### 3. 货币格式化

```vue
<template>
  <div>
    <!-- 自动使用当前语言的货币符号 -->
    <span>{{ formatCurrency(300) }}</span>  <!-- $300.00 或 ¥300.00 -->
    
    <!-- 指定语言的货币格式 -->
    <span>{{ formatCurrency(300, 'pt-BR') }}</span>  <!-- R$300.00 -->
  </div>
</template>

<script setup>
import { useCurrency } from '@/composables/useI18n'

const { format: formatCurrency } = useCurrency()
</script>
```

### 4. 语言切换

```vue
<template>
  <div>
    <!-- 使用语言切换组件 -->
    <LanguageSwitcher />
    
    <!-- 或手动切换 -->
    <select @change="handleLanguageChange">
      <option v-for="locale in supportedLocales" :key="locale.code" :value="locale.code">
        {{ locale.flag }} {{ locale.name }}
      </option>
    </select>
  </div>
</template>

<script setup>
import { useI18n } from '@/composables/useI18n'
import LanguageSwitcher from '@/components/LanguageSwitcher.vue'

const { switchLocale, supportedLocales } = useI18n()

function handleLanguageChange(event) {
  switchLocale(event.target.value)
}
</script>
```

### 5. 日期时间格式化

```vue
<template>
  <div>
    <!-- 相对时间 -->
    <span>{{ formatRelativeTime(transaction.time) }}</span>
    
    <!-- 12小时制时间 -->
    <span>{{ formatTime12Hour(transaction.time) }}</span>
    
    <!-- 标准日期格式 -->
    <span>{{ formatDate(transaction.date, 'long') }}</span>
  </div>
</template>

<script setup>
import { useDateTime } from '@/composables/useI18n'

const { formatRelativeTime, formatTime12Hour, formatDate } = useDateTime()
</script>
```

## 配置选项

### 语言检测优先级
1. localStorage中保存的语言设置
2. 浏览器语言设置
3. 默认语言 (en-US)

### 货币符号配置
每种语言都有对应的货币符号：
- 中文 (zh-CN): ¥ (人民币)
- 英文 (en-US): $ (美元)
- 法文 (fr-FR): € (欧元)
- 西班牙文 (es-ES): € (欧元)
- 葡萄牙文 (pt-BR): R$ (巴西雷亚尔)

### 数字格式化
支持不同语言的数字格式化规则：
```typescript
// 中文: ¥1,234.56
// 英文: $1,234.56
// 法文: 1 234,56 €
// 西班牙文: 1.234,56 €
// 葡萄牙文: R$ 1.234,56
```

## 添加新语言

### 1. 创建语言文件
在 `src/locales/` 目录下创建新的语言文件，例如 `ja-JP.ts`：

```typescript
export default {
  common: {
    currency: '¥',
    loading: '読み込み中...',
    // ... 其他翻译
  },
  // ... 完整的翻译结构
}
```

### 2. 更新配置
在 `src/plugins/i18n.ts` 中添加新语言：

```typescript
import jaJP from '@/locales/ja-JP'

export const SUPPORTED_LOCALES = [
  // ... 现有语言
  {
    code: 'ja-JP',
    name: '日本語',
    flag: '🇯🇵',
    currency: '¥',
    rtl: false
  }
]

// 在messages中添加
messages: {
  // ... 现有语言
  'ja-JP': jaJP
}
```

## 最佳实践

### 1. 翻译键命名
- 使用点分隔的层级结构
- 键名使用camelCase
- 保持键名的语义化和一致性

```typescript
// ✅ 好的命名
'deposit.errors.amountTooLow'
'transactions.status.inProcess'
'common.loading'

// ❌ 避免的命名
'error1'
'status_in_process'
'loading_text'
```

### 2. 参数插值
- 使用有意义的参数名
- 在所有语言中保持参数名一致

```typescript
// ✅ 好的插值
'deposit.errors.amountTooLow': 'Deposit amount cannot less than {min}'

// ❌ 避免的插值
'deposit.errors.amountTooLow': 'Amount cannot less than {0}'
```

### 3. 文本长度考虑
- 德语和法语通常比英语长20-30%
- 中文通常比英语短
- 在设计UI时预留足够空间

### 4. 文化适应
- 考虑不同地区的日期格式
- 注意货币符号的位置
- 考虑颜色和图标的文化含义

## 测试

### 1. 翻译完整性测试
```typescript
// 检查所有语言是否包含相同的键
function checkTranslationCompleteness() {
  const baseKeys = extractKeys(enUS)
  SUPPORTED_LOCALES.forEach(locale => {
    const localeKeys = extractKeys(messages[locale.code])
    const missingKeys = baseKeys.filter(key => !localeKeys.includes(key))
    if (missingKeys.length > 0) {
      console.warn(`Missing keys in ${locale.code}:`, missingKeys)
    }
  })
}
```

### 2. 参数插值测试
```typescript
// 测试参数插值是否正常工作
function testInterpolation() {
  const result = t('deposit.errors.amountTooLow', { min: 100 })
  expect(result).toContain('100')
}
```

### 3. 货币格式化测试
```typescript
// 测试不同语言的货币格式化
function testCurrencyFormatting() {
  expect(formatCurrency(100, 'en-US')).toBe('$100.00')
  expect(formatCurrency(100, 'zh-CN')).toBe('¥100.00')
  expect(formatCurrency(100, 'pt-BR')).toBe('R$100.00')
}
```

## 性能优化

### 1. 懒加载
对于大型应用，可以考虑按需加载语言文件：

```typescript
// 动态导入语言文件
async function loadLocale(locale: SupportedLocale) {
  const messages = await import(`@/locales/${locale}.ts`)
  i18n.global.setLocaleMessage(locale, messages.default)
}
```

### 2. 缓存策略
- 语言设置保存在localStorage
- 避免重复的翻译计算
- 使用computed缓存格式化结果

这个多语言配置为项目提供了完整的国际化支持，确保用户可以使用自己熟悉的语言和货币格式。
