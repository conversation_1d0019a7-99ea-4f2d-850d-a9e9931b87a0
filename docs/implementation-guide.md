# 实现指南文档

## 概述

基于深度API分析和数据流研究，本文档提供了重构实现的详细指南，确保新系统能够完全复制原网站的功能和行为。

## 1. 核心数据模型

### 用户余额模型
```typescript
interface UserBalance {
  uid: number;
  coin: number;        // 当前余额
  dcoin: number;       // 可提现余额
  ecoin: number;       // 有效余额
  bonus: number;       // 奖励余额
  totalcoin: number;   // 总余额
  kyc: number;         // KYC状态 (1=已验证)
  svip: number;        // VIP状态
  ispayer: number;     // 付费用户状态
  nodislabelid: number; // 标签ID
}
```

### 支付渠道模型
```typescript
interface PaymentChannel {
  id: number;
  title: string;       // "AliPay" | "upi"
  icon: string;        // 图标URL
  subtype: string;     // "onlinepay"
  mincoin: number;     // 最小金额
  maxcoin: number;     // 最大金额
  disrate: string;     // 显示费率 "+500" | "+3.00%"
  discoin: number;     // 固定奖励金额 (0表示按百分比)
  type: number;
  pages: PaymentPage[];
}

interface PaymentPage {
  id: number;
  title: string;       // "test1009" | "Paytm APP"
  type: number;
  banktype: number;
  mincoin: number;
  maxcoin: number;
  disrate: string;
  discoin: number;
  rate: number | string; // 实际费率
}
```

### 交易记录模型
```typescript
interface Transaction {
  id: number;
  orderid: string;     // 订单ID
  coin: string;        // 金额 (字符串格式)
  status_str: string;  // 状态文本
  status: number;      // 状态码
  time: string;        // 时间 "06:27 pm"
  title: string;       // 支付方式或描述
  memo: string;        // 备注
}

interface TransactionHistory {
  deplist: Record<string, Transaction[]>;  // 存款记录，按日期分组
  drawlist: Transaction[];                 // 提现记录
  betlist: Transaction[];                  // 投注记录
  bonuslist: Record<string, Transaction[]>; // 奖励记录，按日期分组
  banklist: any[];                         // 银行相关记录
  show: number;                            // 显示标识
  url: string;                             // 客服URL
}
```

## 2. 关键业务逻辑实现

### 奖励计算逻辑
```typescript
function calculateBonus(amount: number, channel: PaymentChannel): number {
  if (channel.discoin > 0) {
    // 固定奖励 (如AliPay的500)
    return channel.discoin;
  } else {
    // 百分比奖励 (如UPI的3%)
    const rate = typeof channel.pages[0].rate === 'string' 
      ? parseFloat(channel.pages[0].rate) 
      : channel.pages[0].rate;
    return amount * rate;
  }
}
```

### 金额验证逻辑
```typescript
function validateAmount(amount: number, channel: PaymentChannel): ValidationResult {
  if (amount < channel.mincoin) {
    return {
      valid: false,
      message: `Deposit amount cannot less than ${channel.mincoin}`
    };
  }
  
  if (amount > channel.maxcoin) {
    return {
      valid: false,
      message: `Deposit amount cannot exceed ${channel.maxcoin}`
    };
  }
  
  return { valid: true };
}
```

### 状态映射逻辑
```typescript
const TRANSACTION_STATUS = {
  DEPOSIT: {
    0: { text: 'In-Process', class: 'status-pending' },
    1: { text: 'Success', class: 'status-success' },
    2: { text: 'Failed', class: 'status-failed' }
  },
  BONUS: {
    34: { text: 'Transfer to Cash Balance', class: 'status-transfer-out' },
    35: { text: 'Login bonus', class: 'status-bonus' }
  }
};

function getStatusDisplay(status: number, type: 'deposit' | 'bonus') {
  const mapping = TRANSACTION_STATUS[type.toUpperCase()];
  return mapping[status] || { text: 'Unknown', class: 'status-unknown' };
}
```

## 3. API集成实现

### API客户端配置
```typescript
const API_BASE_URL = 'http://service.haiwailaba.cyou';

const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
});

// 响应拦截器
apiClient.interceptors.response.use(
  (response) => {
    if (response.data.code !== 0) {
      throw new Error(response.data.msg || 'API Error');
    }
    return response.data.data;
  },
  (error) => {
    console.error('API Error:', error);
    throw error;
  }
);
```

### 具体API调用
```typescript
// 获取用户余额和支付配置
async function getUserBalance(coin: number = 300): Promise<UserBalanceResponse> {
  return apiClient.get(`/user/balance?cat=2&coin=${coin}&paypop=0`);
}

// 提交充值订单
async function submitDepositOrder(orderData: DepositOrderData): Promise<OrderResponse> {
  return apiClient.post('/order/pay', orderData);
}

// 获取交易历史
async function getTransactionHistory(page: number = 1, size: number = 10): Promise<TransactionHistory> {
  return apiClient.get(`/user/history?num=${page}&size=${size}`);
}

// 获取提现信息
async function getWithdrawInfo(): Promise<WithdrawInfo> {
  return apiClient.get('/draw/index?drawpop=0');
}

// 获取银行账户信息
async function getBankAccounts(split?: boolean): Promise<BankAccountsResponse> {
  const params = split ? '?split=1' : '';
  return apiClient.get(`/user/banks${params}`);
}
```

## 4. 状态管理实现

### Pinia Store结构
```typescript
// stores/user.ts
export const useUserStore = defineStore('user', {
  state: () => ({
    balance: null as UserBalance | null,
    paymentChannels: [] as PaymentChannel[],
    selectedChannel: null as PaymentChannel | null,
    selectedAmount: 0,
    loading: false,
    error: null as string | null
  }),
  
  getters: {
    currentBalance: (state) => state.balance?.coin || 0,
    canWithdraw: (state) => (state.balance?.dcoin || 0) > 0,
    selectedChannelLimits: (state) => {
      if (!state.selectedChannel) return null;
      return {
        min: state.selectedChannel.mincoin,
        max: state.selectedChannel.maxcoin
      };
    }
  },
  
  actions: {
    async fetchUserBalance(coin: number = 300) {
      this.loading = true;
      try {
        const data = await getUserBalance(coin);
        this.balance = data.user;
        this.paymentChannels = data.channels;
        this.error = null;
      } catch (error) {
        this.error = error.message;
      } finally {
        this.loading = false;
      }
    },
    
    selectPaymentChannel(channel: PaymentChannel) {
      this.selectedChannel = channel;
    },
    
    calculateBonus(): number {
      if (!this.selectedChannel) return 0;
      return calculateBonus(this.selectedAmount, this.selectedChannel);
    }
  }
});
```

## 5. 组件实现要点

### 充值页面组件
```vue
<template>
  <div class="deposit-page">
    <!-- 余额显示 -->
    <div class="balance-display">
      <span>Current Balance</span>
      <span>₹{{ formatCurrency(userStore.currentBalance) }}</span>
    </div>
    
    <!-- 金额输入 -->
    <div class="amount-input">
      <input 
        v-model="amount" 
        type="number" 
        @input="onAmountChange"
        :class="{ error: validationError }"
      />
      <div v-if="validationError" class="error-message">
        {{ validationError }}
      </div>
    </div>
    
    <!-- 预设金额 -->
    <div class="preset-amounts">
      <button 
        v-for="coin in presetCoins" 
        :key="coin"
        @click="selectAmount(coin)"
        :class="{ active: amount === coin }"
      >
        {{ coin }}
      </button>
    </div>
    
    <!-- 支付方式 -->
    <div class="payment-methods">
      <div 
        v-for="channel in paymentChannels" 
        :key="channel.id"
        @click="selectChannel(channel)"
        :class="{ active: selectedChannel?.id === channel.id }"
        class="payment-method-card"
      >
        <div class="bonus-display">{{ channel.disrate }}</div>
        <div class="method-name">{{ channel.title }}</div>
      </div>
    </div>
    
    <!-- 计算结果显示 -->
    <div class="calculation-display">
      <div class="cash-balance">
        <span>Cash Balance</span>
        <span>₹{{ formatCurrency(amount) }}</span>
      </div>
      <div class="cash-bonus">
        <span>Cash Bonus</span>
        <span>₹{{ formatCurrency(calculatedBonus) }}</span>
      </div>
    </div>
    
    <!-- 提交按钮 -->
    <button 
      @click="submitDeposit"
      :disabled="!canSubmit"
      class="deposit-button"
    >
      DEPOSIT NOW
    </button>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { useUserStore } from '@/stores/user';

const userStore = useUserStore();
const amount = ref(300);
const validationError = ref('');

const selectedChannel = computed(() => userStore.selectedChannel);
const paymentChannels = computed(() => userStore.paymentChannels);
const presetCoins = computed(() => userStore.balance?.coins || []);

const calculatedBonus = computed(() => {
  if (!selectedChannel.value) return 0;
  return calculateBonus(amount.value, selectedChannel.value);
});

const canSubmit = computed(() => {
  return amount.value > 0 && 
         selectedChannel.value && 
         !validationError.value;
});

function onAmountChange() {
  if (selectedChannel.value) {
    const validation = validateAmount(amount.value, selectedChannel.value);
    validationError.value = validation.valid ? '' : validation.message;
  }
}

function selectAmount(coin: number) {
  amount.value = coin;
  onAmountChange();
}

function selectChannel(channel: PaymentChannel) {
  userStore.selectPaymentChannel(channel);
  onAmountChange();
}

async function submitDeposit() {
  try {
    const orderData = {
      amount: amount.value,
      channel_id: selectedChannel.value.id,
      page_id: selectedChannel.value.pages[0].id
    };
    
    await submitDepositOrder(orderData);
    // 处理成功响应
  } catch (error) {
    // 处理错误
    console.error('Deposit failed:', error);
  }
}

// 页面加载时获取数据
onMounted(() => {
  userStore.fetchUserBalance(amount.value);
});

// 监听金额变化，重新获取配置
watch(amount, (newAmount) => {
  if (newAmount > 0) {
    userStore.fetchUserBalance(newAmount);
  }
});
</script>
```

## 6. 错误处理实现

### 全局错误处理
```typescript
// composables/useErrorHandler.ts
export function useErrorHandler() {
  const { $toast } = useNuxtApp();
  
  function handleAPIError(error: any, context?: string) {
    let message = '操作失败，请稍后重试';
    
    if (error.response) {
      switch (error.response.status) {
        case 400:
          message = '请求参数错误';
          break;
        case 401:
          message = '请先登录';
          break;
        case 403:
          message = '没有操作权限';
          break;
        case 404:
          message = '请求的资源不存在';
          break;
        case 500:
          message = '服务器内部错误';
          break;
      }
    } else if (error.message) {
      message = error.message;
    }
    
    if (context) {
      message = `${context}: ${message}`;
    }
    
    $toast.error(message);
    console.error('API Error:', error);
  }
  
  return { handleAPIError };
}
```

## 7. 测试策略

### 单元测试重点
- API响应数据解析
- 业务逻辑计算 (奖励计算、验证逻辑)
- 状态管理 (Pinia store)
- 工具函数 (格式化、验证等)

### 集成测试重点
- API集成测试
- 页面交互流程
- 错误处理流程
- 数据流完整性

### E2E测试场景
- 完整充值流程
- 交易记录查看
- 支付方式管理
- 错误场景处理

这个实现指南确保了新系统能够准确复制原网站的所有功能和行为特征。
