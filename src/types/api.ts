// API响应基础类型
export interface ApiResponse<T = any> {
  code: number
  msg: string
  data: T
}

// 用户余额信息
export interface UserBalance {
  uid: number
  coin: number        // 当前余额
  dcoin: number       // 可提现余额
  ecoin: number       // 有效余额
  bonus: number       // 奖励余额
  totalcoin: number   // 总余额
  kyc: number         // KYC状态 (1=已验证)
  svip: number        // VIP状态
  ispayer: number     // 付费用户状态
  nodislabelid: number // 标签ID
}

// 支付渠道页面信息
export interface PaymentPage {
  id: number
  title: string       // "test1009" | "Paytm APP"
  type: number
  banktype: number
  mincoin: number
  maxcoin: number
  disrate: string
  discoin: number
  rate: number | string // 实际费率
}

// 支付渠道信息
export interface PaymentChannel {
  id: number
  title: string       // "AliPay" | "upi"
  icon: string        // 图标URL
  subtype: string     // "onlinepay"
  mincoin: number     // 最小金额
  maxcoin: number     // 最大金额
  disrate: string     // 显示费率 "+500" | "+3.00%"
  discoin: number     // 固定奖励金额 (0表示按百分比)
  type: number
  pages: PaymentPage[]
}

// 用户余额API响应
export interface UserBalanceResponse {
  user: UserBalance
  coins: number[]     // 预设金额
  channels: PaymentChannel[]
  memo: string        // 提示信息
  popmsg: string      // 弹窗消息
  customer: any[]     // 客服信息
  url: string         // 客服URL
}

// 交易记录
export interface Transaction {
  id: number
  orderid: string     // 订单ID
  coin: string        // 金额 (字符串格式)
  status_str: string  // 状态文本
  status: number      // 状态码
  time: string        // 时间 "06:27 pm"
  title: string       // 支付方式或描述
  memo: string        // 备注
}

// 交易历史响应
export interface TransactionHistory {
  deplist: Record<string, Transaction[]>  // 存款记录，按日期分组
  drawlist: Transaction[]                 // 提现记录
  betlist: Transaction[]                  // 投注记录
  bonuslist: Record<string, Transaction[]> // 奖励记录，按日期分组
  banklist: any[]                         // 银行相关记录
  show: number                            // 显示标识
  url: string                             // 客服URL
}

// 提现信息
export interface WithdrawInfo {
  uid: number
  dcoin: number       // 可提现余额
  limit: number       // 提现限制
  mincoin: number     // 最小提现金额
  maxcoin: number     // 最大提现金额
  memo: string        // 提示信息
  popmsg: string      // 弹窗消息
  customer: any[]     // 客服信息
  url: string         // 客服URL
}

// UPI钱包信息
export interface UPIWallet {
  id: number
  name: string        // "Paytm" | "Phonepe" | "Gpay"
  checked: boolean    // false=已绑定, true=未绑定
  card: string        // 绑定的账户ID或"Link"
  icon: string        // 图标路径
  cat: number         // 钱包类别
}

// 银行账户信息响应
export interface BankAccountsResponse {
  banks: any[]        // 银行账户列表
  upis?: Record<string, UPIWallet> // UPI钱包信息 (仅当split=1时返回)
  kyc: number         // KYC验证状态
  url: string         // 客服URL
}

// 订单提交数据
export interface DepositOrderData {
  amount: number
  channel_id: number
  page_id: number
}

// 订单响应
export interface OrderResponse {
  // 具体结构需要根据实际API响应确定
  order_id?: string
  payment_url?: string
  status?: string
}

// 表单验证结果
export interface ValidationResult {
  valid: boolean
  message?: string
}

// 银行账户表单数据
export interface BankAccountFormData {
  accountNumber: string
  confirmAccountNumber: string
  ifscCode: string
  name: string
  bankProof?: File
}

// 错误响应
export interface ApiError {
  code: number
  message: string
  details?: any
}
