import type { PaymentChannel, ValidationResult } from '@/types/api'

/**
 * 计算奖励金额
 * @param amount 充值金额
 * @param channel 支付渠道
 * @returns 奖励金额
 */
export function calculateBonus(amount: number, channel: PaymentChannel): number {
  if (!channel || amount <= 0) return 0

  if (channel.discoin > 0) {
    // 固定奖励 (如AliPay的500)
    return channel.discoin
  } else {
    // 百分比奖励 (如UPI的3%)
    const rate =
      typeof channel.pages[0]?.rate === 'string'
        ? parseFloat(channel.pages[0].rate)
        : channel.pages[0]?.rate || 0
    return amount * rate
  }
}

/**
 * 验证充值金额
 * @param amount 充值金额
 * @param channel 支付渠道
 * @returns 验证结果
 */
export function validateAmount(amount: number, channel: PaymentChannel): ValidationResult {
  if (!channel) {
    return {
      valid: false,
      message: 'Please select a payment method'
    }
  }

  if (amount < channel.mincoin) {
    return {
      valid: false,
      message: `Deposit amount cannot less than ${channel.mincoin}`
    }
  }

  if (amount > channel.maxcoin) {
    return {
      valid: false,
      message: `Deposit amount cannot exceed ${channel.maxcoin}`
    }
  }

  return { valid: true }
}

/**
 * 格式化货币显示
 * @param amount 金额
 * @param currency 货币符号
 * @returns 格式化后的字符串
 */
export function formatCurrency(amount: number | string, currency: string = '$'): string {
  const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount

  if (isNaN(numAmount)) return `${currency}0.00`

  return `${currency}${numAmount.toFixed(2)}`
}

/**
 * 解析API错误信息
 * @param error 错误对象
 * @returns 用户友好的错误信息
 */
export function parseApiError(error: any): string {
  // 业务错误
  if (error.code && error.message) {
    return error.message
  }

  // HTTP错误
  if (error.response) {
    const status = error.response.status
    switch (status) {
      case 400:
        return 'Invalid request parameters'
      case 401:
        return 'Authentication required'
      case 403:
        return 'Access denied'
      case 404:
        return 'Service not found'
      case 500:
        return 'Server error, please try again later'
      default:
        return `HTTP Error ${status}`
    }
  }

  // 网络错误
  if (error.code === 'NETWORK_ERROR' || !error.response) {
    return 'Network connection failed'
  }

  return error.message || 'Unknown error occurred'
}

/**
 * 交易状态映射
 */
export const TRANSACTION_STATUS = {
  DEPOSIT: {
    0: { text: 'In-Process', class: 'status-pending', color: 'warning' },
    1: { text: 'Success', class: 'status-success', color: 'success' },
    2: { text: 'Failed', class: 'status-failed', color: 'error' }
  },
  BONUS: {
    34: { text: 'Transfer to Cash Balance', class: 'status-transfer-out', color: 'info' },
    35: { text: 'Login bonus', class: 'status-bonus', color: 'success' }
  }
} as const

/**
 * 获取交易状态显示信息
 * @param status 状态码
 * @param type 交易类型
 * @returns 状态显示信息
 */
export function getTransactionStatus(status: number, type: 'deposit' | 'bonus' = 'deposit') {
  const mapping = TRANSACTION_STATUS[type.toUpperCase() as keyof typeof TRANSACTION_STATUS]
  return (
    mapping[status as keyof typeof mapping] || {
      text: 'Unknown',
      class: 'status-unknown',
      color: 'default'
    }
  )
}

/**
 * 格式化时间显示
 * @param timeStr 时间字符串 "06:27 pm"
 * @returns 格式化后的时间
 */
export function formatTime(timeStr: string): string {
  // API返回的时间格式已经是用户友好的格式，直接返回
  return timeStr
}

/**
 * 格式化日期显示
 * @param dateStr 日期字符串 "08/05/2025"
 * @returns 格式化后的日期
 */
export function formatDate(dateStr: string): string {
  // API返回的日期格式已经是用户友好的格式，直接返回
  return dateStr
}

/**
 * 检查是否为有效的IFSC代码
 * @param ifsc IFSC代码
 * @returns 是否有效
 */
export function isValidIFSC(ifsc: string): boolean {
  // IFSC代码格式：4位字母 + 0 + 6位字母数字
  const ifscPattern = /^[A-Z]{4}0[A-Z0-9]{6}$/
  return ifscPattern.test(ifsc.toUpperCase())
}

/**
 * 检查银行账户号码是否有效
 * @param accountNumber 账户号码
 * @returns 是否有效
 */
export function isValidAccountNumber(accountNumber: string): boolean {
  // 银行账户号码：9-18位数字
  const accountPattern = /^\d{9,18}$/
  return accountPattern.test(accountNumber)
}

/**
 * 生成随机订单ID
 * @returns 订单ID
 */
export function generateOrderId(): string {
  const timestamp = Date.now()
  const random = Math.floor(Math.random() * 10000)
    .toString()
    .padStart(4, '0')
  return `${timestamp}${random}`
}

/**
 * 防抖函数
 * @param func 要防抖的函数
 * @param delay 延迟时间
 * @returns 防抖后的函数
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let timeoutId: ReturnType<typeof setTimeout>

  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId)
    timeoutId = setTimeout(() => func(...args), delay)
  }
}

/**
 * 节流函数
 * @param func 要节流的函数
 * @param delay 延迟时间
 * @returns 节流后的函数
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let lastCall = 0

  return (...args: Parameters<T>) => {
    const now = Date.now()
    if (now - lastCall >= delay) {
      lastCall = now
      func(...args)
    }
  }
}
