<template>
  <div class="main-layout min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- 顶部导航栏 -->
    <header
      class="fixed top-0 left-0 right-0 z-50 bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700"
    >
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
          <!-- Logo和标题 -->
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <h1 class="text-xl font-bold text-gray-900 dark:text-white">PaySMS</h1>
            </div>
          </div>

          <!-- 导航菜单 -->
          <nav class="hidden md:flex space-x-8">
            <router-link
              v-for="item in navigationItems"
              :key="item.name"
              :to="item.path"
              :class="[
                'px-3 py-2 rounded-md text-sm font-medium transition-colors',
                isActiveRoute(item.path)
                  ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300'
                  : 'text-gray-500 hover:text-gray-700 dark:text-gray-300 dark:hover:text-white'
              ]"
            >
              <div class="flex items-center gap-2">
                <span class="text-lg">{{ item.icon }}</span>
                <span>{{ t(item.label) }}</span>
              </div>
            </router-link>
          </nav>

          <!-- 右侧工具栏 -->
          <div class="flex items-center gap-4">
            <!-- 用户余额显示 -->
            <div
              class="hidden sm:flex items-center gap-2 px-3 py-1 bg-gray-100 dark:bg-gray-700 rounded-lg"
            >
              <span class="text-sm text-gray-600 dark:text-gray-300"
                >{{ t('deposit.currentBalance') }}:</span
              >
              <span class="font-semibold text-gray-900 dark:text-white">{{
                formatCurrency(userBalance)
              }}</span>
            </div>

            <!-- 语言切换 -->
            <LanguageSwitcher />

            <!-- 主题切换 -->
            <NButton quaternary circle @click="toggleTheme">
              <template #icon>
                <div class="text-lg">{{ theme === 'light' ? '🌙' : '☀️' }}</div>
              </template>
            </NButton>

            <!-- 移动端菜单按钮 -->
            <NButton quaternary circle class="md:hidden" @click="mobileMenuOpen = !mobileMenuOpen">
              <template #icon>
                <div class="text-lg">☰</div>
              </template>
            </NButton>
          </div>
        </div>
      </div>

      <!-- 移动端导航菜单 -->
      <div v-if="mobileMenuOpen" class="md:hidden border-t border-gray-200 dark:border-gray-700">
        <div class="px-2 pt-2 pb-3 space-y-1">
          <router-link
            v-for="item in navigationItems"
            :key="item.name"
            :to="item.path"
            :class="[
              'block px-3 py-2 rounded-md text-base font-medium transition-colors',
              isActiveRoute(item.path)
                ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300'
                : 'text-gray-500 hover:text-gray-700 dark:text-gray-300 dark:hover:text-white'
            ]"
            @click="mobileMenuOpen = false"
          >
            <div class="flex items-center gap-3">
              <span class="text-lg">{{ item.icon }}</span>
              <span>{{ t(item.label) }}</span>
            </div>
          </router-link>
        </div>
      </div>
    </header>

    <!-- 主内容区域 -->
    <main class="flex-1 pt-16 pb-20 md:pb-8">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- 页面标题 -->
        <div v-if="currentPageTitle" class="mb-6">
          <h2 class="text-2xl font-bold text-gray-900 dark:text-white">
            {{ t(currentPageTitle) }}
          </h2>
        </div>

        <!-- 路由视图 -->
        <router-view v-slot="{ Component, route }">
          <transition name="page-transition" mode="out-in" appear>
            <keep-alive :include="keepAliveRoutes">
              <component :is="Component" :key="route.path" />
            </keep-alive>
          </transition>
        </router-view>
      </div>
    </main>

    <!-- 底部导航（移动端） -->
    <footer
      class="fixed bottom-0 left-0 right-0 z-50 md:hidden bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700"
    >
      <div class="grid grid-cols-5 gap-1 py-2">
        <router-link
          v-for="item in navigationItems"
          :key="item.name"
          :to="item.path"
          :class="[
            'flex flex-col items-center justify-center py-2 px-1 text-xs transition-colors',
            isActiveRoute(item.path)
              ? 'text-blue-600 dark:text-blue-400'
              : 'text-gray-500 dark:text-gray-400'
          ]"
        >
          <span class="text-lg mb-1">{{ item.icon }}</span>
          <span class="truncate">{{ t(item.label) }}</span>
        </router-link>
      </div>
    </footer>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRoute } from 'vue-router'
import { NButton } from 'naive-ui'
import { useI18n, useCurrency } from '@/composables/useI18n'
import { useNaive } from '@/composables/useNaive'
import { useUserStore } from '@/stores/user'
import LanguageSwitcher from '@/components/LanguageSwitcher.vue'
import { ROUTE_PATHS } from '@/router'

const route = useRoute()
const { t } = useI18n()
const { format: formatCurrency } = useCurrency()
const { theme, toggleTheme } = useNaive()
const userStore = useUserStore()

// 移动端菜单状态
const mobileMenuOpen = ref(false)

// 用户余额从store获取
const userBalance = computed(() => userStore.currentBalance)

// 导航菜单项
const navigationItems = [
  {
    name: 'deposit',
    path: ROUTE_PATHS.DEPOSIT_ADD,
    label: 'nav.deposit',
    icon: '💰'
  },
  {
    name: 'withdraw',
    path: ROUTE_PATHS.WITHDRAW_INDEX,
    label: 'nav.withdraw',
    icon: '💸'
  },
  {
    name: 'transactions',
    path: ROUTE_PATHS.TRANSACTION_TRANSACTION,
    label: 'nav.transactions',
    icon: '📊'
  },
  {
    name: 'payments',
    path: ROUTE_PATHS.DEPOSIT_MANAGE_PAYMENT,
    label: 'nav.payments',
    icon: '💳'
  }
]

// 需要缓存的路由
const keepAliveRoutes = ['TransactionTransaction']

// 当前页面标题
const currentPageTitle = computed(() => {
  return (route.meta.title as string) || ''
})

// 检查是否为活跃路由
function isActiveRoute(path: string) {
  return route.path === path
}
</script>

<style scoped>
/* 页面切换动画 */
.page-transition-enter-active,
.page-transition-leave-active {
  transition: all 0.3s ease;
}

.page-transition-enter-from {
  opacity: 0;
  transform: translateX(20px);
}

.page-transition-leave-to {
  opacity: 0;
  transform: translateX(-20px);
}

/* 移动端底部导航样式 */
@media (max-width: 768px) {
  .main-layout {
    padding-bottom: 80px; /* 为底部导航留出空间 */
  }
}
</style>
