import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'

// 导入UnoCSS样式
import 'uno.css'

// 导入全局样式
import './styles/global.scss'

// 导入i18n
import i18n from './plugins/i18n'

// 导入路由
import router from './router'

// 创建应用实例
const app = createApp(App)

// 创建Pinia实例
const pinia = createPinia()

// 使用插件
app.use(pinia)
app.use(i18n)
app.use(router)

// 挂载应用
app.mount('#app')
