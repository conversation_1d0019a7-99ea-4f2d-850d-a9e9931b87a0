import { createRouter, createWebHashHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'

// 路由配置 - 保持与原网站完全一致的路径结构
const routes: RouteRecordRaw[] = [
  {
    path: '/',
    redirect: '/pages/deposit/add/add'
  },
  {
    path: '/pages',
    component: () => import('@/layouts/MainLayout.vue'),
    children: [
      // 充值页面 - 对应原网站 /#/pages/deposit/add/add
      {
        path: 'deposit/add/add',
        name: 'DepositAdd',
        component: () => import('@/views/recharge/index.vue'),
        meta: {
          title: 'deposit.title',
          requiresAuth: true,
          keepAlive: false
        }
      },
      
      // 提现页面 - 对应原网站 /#/pages/withdraw/index
      {
        path: 'withdraw/index',
        name: 'WithdrawIndex',
        component: () => import('@/views/withdraw/index.vue'),
        meta: {
          title: 'withdraw.title',
          requiresAuth: true,
          keepAlive: false
        }
      },
      
      // 交易记录页面 - 对应原网站 /#/pages/transaction/transaction
      {
        path: 'transaction/transaction',
        name: 'TransactionTransaction',
        component: () => import('@/views/transactions/index.vue'),
        meta: {
          title: 'transactions.title',
          requiresAuth: true,
          keepAlive: true // 交易记录页面保持缓存
        }
      },
      
      // 支付管理页面 - 对应原网站 /#/pages/deposit/managepayment/managepayment
      {
        path: 'deposit/managepayment/managepayment',
        name: 'DepositManagePayment',
        component: () => import('@/views/payments/index.vue'),
        meta: {
          title: 'payments.title',
          requiresAuth: true,
          keepAlive: false
        }
      },
      
      // 添加银行卡页面 - 对应原网站 /#/pages/deposit/addcreditcard/addcreditcard
      {
        path: 'deposit/addcreditcard/addcreditcard',
        name: 'DepositAddCreditCard',
        component: () => import('@/views/withdraw/chanels/add/index.vue'),
        meta: {
          title: 'addBankAccount.title',
          requiresAuth: true,
          keepAlive: false
        }
      }
    ]
  },
  
  // 404页面
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/error/404.vue'),
    meta: {
      title: 'common.notFound'
    }
  }
]

// 创建路由实例
const router = createRouter({
  // 使用hash模式以保持与原网站一致
  history: createWebHashHistory(),
  routes,
  scrollBehavior(_to, _from, savedPosition) {
    // 路由切换时的滚动行为
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 全局前置守卫
router.beforeEach(async (to, _from, next) => {
  // 设置页面标题
  if (to.meta.title) {
    // 这里可以使用i18n来翻译标题
    document.title = `PaySMS - ${String(to.meta.title)}`
  }
  
  // 身份验证检查
  if (to.meta.requiresAuth) {
    // TODO: 实现身份验证逻辑
    // const isAuthenticated = await checkAuth()
    // if (!isAuthenticated) {
    //   next('/login')
    //   return
    // }
  }
  
  next()
})

// 全局后置钩子
router.afterEach((to, from) => {
  // 路由切换完成后的处理
  console.log(`Route changed from ${from.path} to ${to.path}`)
})

export default router

// 导出路由相关的类型和工具函数
export type { RouteRecordRaw }

// 路由名称常量
export const ROUTE_NAMES = {
  DEPOSIT_ADD: 'DepositAdd',
  WITHDRAW_INDEX: 'WithdrawIndex', 
  TRANSACTION_TRANSACTION: 'TransactionTransaction',
  DEPOSIT_MANAGE_PAYMENT: 'DepositManagePayment',
  DEPOSIT_ADD_CREDIT_CARD: 'DepositAddCreditCard',
  NOT_FOUND: 'NotFound'
} as const

// 路由路径常量
export const ROUTE_PATHS = {
  DEPOSIT_ADD: '/pages/deposit/add/add',
  WITHDRAW_INDEX: '/pages/withdraw/index',
  TRANSACTION_TRANSACTION: '/pages/transaction/transaction', 
  DEPOSIT_MANAGE_PAYMENT: '/pages/deposit/managepayment/managepayment',
  DEPOSIT_ADD_CREDIT_CARD: '/pages/deposit/addcreditcard/addcreditcard'
} as const

// 路由导航工具函数
export function useRouterNavigation() {
  return {
    goToDeposit: () => router.push(ROUTE_PATHS.DEPOSIT_ADD),
    goToWithdraw: () => router.push(ROUTE_PATHS.WITHDRAW_INDEX),
    goToTransactions: () => router.push(ROUTE_PATHS.TRANSACTION_TRANSACTION),
    goToPayments: () => router.push(ROUTE_PATHS.DEPOSIT_MANAGE_PAYMENT),
    goToAddBankAccount: () => router.push(ROUTE_PATHS.DEPOSIT_ADD_CREDIT_CARD)
  }
}
