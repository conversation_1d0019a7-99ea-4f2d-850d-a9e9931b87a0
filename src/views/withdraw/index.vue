<template>
  <div class="withdraw-page">
    <!-- 加载状态 -->
    <div v-if="withdrawStore.loading" class="flex justify-center items-center py-12">
      <NSpin size="large">
        <template #description>{{ t('common.loading') }}</template>
      </NSpin>
    </div>

    <!-- 主要内容 -->
    <div v-else class="space-y-6">
      <!-- 可提现余额显示 -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div class="flex justify-between items-center">
          <div>
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-1">
              {{ t('withdraw.withdrawableBalance') }}
            </h3>
            <p class="text-sm text-gray-500 dark:text-gray-400">
              {{ t('withdraw.withdrawMin') }}:
              {{ formatCurrency(withdrawStore.minWithdrawAmount) }} -
              {{ t('withdraw.withdrawMax') }}: {{ formatCurrency(withdrawStore.maxWithdrawAmount) }}
            </p>
          </div>
          <div class="text-right">
            <div class="text-2xl font-bold text-green-600 dark:text-green-400">
              {{ formatCurrency(withdrawStore.withdrawableBalance) }}
            </div>
            <div
              v-if="withdrawStore.hasWithdrawLimit"
              class="text-sm text-orange-600 dark:text-orange-400"
            >
              {{ t('withdraw.withdrawLimit') }}: {{ withdrawStore.withdrawInfo?.limit }}
            </div>
          </div>
        </div>
      </div>

      <!-- 银行账户状态 -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h4 class="text-md font-semibold text-gray-900 dark:text-white mb-4">
          {{ t('payments.myBankAccounts') }}
        </h4>

        <!-- 有银行账户时 -->
        <div v-if="withdrawStore.hasBankAccounts" class="space-y-3">
          <div
            v-for="(account, index) in withdrawStore.bankAccounts"
            :key="index"
            class="p-4 border border-gray-200 dark:border-gray-600 rounded-lg"
          >
            <div class="flex items-center justify-between">
              <div>
                <h5 class="font-medium text-gray-900 dark:text-white">
                  {{ account.bank_name || 'Bank Account' }}
                </h5>
                <p class="text-sm text-gray-500 dark:text-gray-400">
                  ****{{ account.account_number?.slice(-4) || '****' }}
                </p>
              </div>
              <div class="text-green-600 dark:text-green-400">
                <div class="i-carbon-checkmark-filled text-xl"></div>
              </div>
            </div>
          </div>
        </div>

        <!-- 无银行账户时 -->
        <div v-else class="text-center py-8">
          <div class="text-4xl mb-4">🏦</div>
          <h5 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
            {{ t('withdraw.noBankAccount') }}
          </h5>
          <p class="text-gray-600 dark:text-gray-300 mb-4">
            {{ t('withdraw.addBankAccountDesc') }}
          </p>
          <NButton type="primary" @click="goToAddBankAccount">
            {{ t('withdraw.addNewBankAccount') }}
          </NButton>
        </div>
      </div>

      <!-- 提现金额输入 (仅在有银行账户时显示) -->
      <div
        v-if="withdrawStore.hasBankAccounts"
        class="bg-white dark:bg-gray-800 rounded-lg shadow p-6"
      >
        <h4 class="text-md font-semibold text-gray-900 dark:text-white mb-4">
          {{ t('withdraw.amount') }}
        </h4>

        <div class="mb-4">
          <NInputNumber
            v-model:value="withdrawStore.selectedAmount"
            :placeholder="t('withdraw.amount')"
            :min="withdrawStore.minWithdrawAmount"
            :max="Math.min(withdrawStore.maxWithdrawAmount, withdrawStore.withdrawableBalance)"
            size="large"
            class="w-full"
            @update:value="handleAmountChange"
          >
            <template #prefix>
              <span class="text-gray-500">{{ currentCurrency }}</span>
            </template>
          </NInputNumber>

          <!-- 快速金额选择 -->
          <div class="mt-4 grid grid-cols-4 gap-3">
            <NButton
              v-for="percentage in [25, 50, 75, 100]"
              :key="percentage"
              size="small"
              @click="selectPercentage(percentage)"
            >
              {{ percentage }}%
            </NButton>
          </div>
        </div>
      </div>

      <!-- 错误提示 -->
      <NAlert
        v-if="withdrawStore.error"
        type="error"
        :title="t('common.error')"
        closable
        @close="withdrawStore.clearError"
      >
        {{ withdrawStore.error }}
      </NAlert>

      <!-- 验证错误提示 -->
      <NAlert
        v-if="validationError"
        type="warning"
        :title="t('common.warning')"
        closable
        @close="validationError = ''"
      >
        {{ validationError }}
      </NAlert>

      <!-- 提现按钮 -->
      <div v-if="withdrawStore.hasBankAccounts" class="text-center">
        <NButton
          type="primary"
          size="large"
          :loading="withdrawStore.loading"
          :disabled="!canSubmit"
          @click="handleSubmit"
        >
          {{ t('withdraw.withdrawNow') }}
        </NButton>
      </div>

      <!-- 提示信息 -->
      <div
        v-if="withdrawStore.memo"
        class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4"
      >
        <h4 class="font-semibold text-blue-800 dark:text-blue-300 mb-2">
          {{ t('withdraw.tips') }}
        </h4>
        <div class="text-sm text-blue-700 dark:text-blue-400" v-html="withdrawStore.memo"></div>
      </div>

      <!-- 连接状态提示 -->
      <div
        v-if="!withdrawStore.loading && withdrawStore.withdrawableBalance === 0"
        class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4 text-center"
      >
        <div class="text-4xl mb-2">⚠️</div>
        <p class="text-yellow-800 dark:text-yellow-300">
          {{ t('withdraw.errors.connectionFailed') }}
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import { NInputNumber, NButton, NSpin, NAlert } from 'naive-ui'
import { useI18n, useCurrency } from '@/composables/useI18n'
import { useNaive } from '@/composables/useNaive'
import { useWithdrawStore } from '@/stores/withdraw'
import { ROUTE_PATHS } from '@/router'

const router = useRouter()
const { t } = useI18n()
const { format: formatCurrency, currency: currentCurrency } = useCurrency()
const { message } = useNaive()
const withdrawStore = useWithdrawStore()

// 本地状态
const validationError = ref('')

// 计算属性
const canSubmit = computed(() => {
  return (
    withdrawStore.canWithdraw &&
    withdrawStore.selectedAmount > 0 &&
    !withdrawStore.loading &&
    withdrawStore.validateWithdrawAmount(withdrawStore.selectedAmount).valid
  )
})

// 方法
function handleAmountChange(value: number | null) {
  if (value !== null) {
    withdrawStore.setWithdrawAmount(value)

    // 实时验证
    const validation = withdrawStore.validateWithdrawAmount(value)
    validationError.value = validation.valid ? '' : validation.message || ''
  }
}

function selectPercentage(percentage: number) {
  const amount = Math.floor((withdrawStore.withdrawableBalance * percentage) / 100)
  withdrawStore.setWithdrawAmount(amount)

  // 清除验证错误
  validationError.value = ''
}

function goToAddBankAccount() {
  router.push(ROUTE_PATHS.DEPOSIT_ADD_CREDIT_CARD)
}

async function handleSubmit() {
  const validation = withdrawStore.validateWithdrawAmount(withdrawStore.selectedAmount)
  if (!validation.valid) {
    message.error(validation.message || t('withdraw.errors.invalidAmount'))
    return
  }

  const success = await withdrawStore.submitWithdrawRequest()
  if (success) {
    message.success(t('withdraw.success.requestSubmitted'))
    // 重置金额
    withdrawStore.setWithdrawAmount(0)
  }
}

// 生命周期
onMounted(async () => {
  await Promise.all([withdrawStore.fetchWithdrawInfo(), withdrawStore.fetchBankAccounts()])
})
</script>

<style scoped>
.withdraw-page {
  @apply w-full;
}
</style>
