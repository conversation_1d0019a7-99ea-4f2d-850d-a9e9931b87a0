<template>
  <div class="add-bank-account-page">
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
      <h3 class="text-lg font-semibold mb-4 text-gray-900 dark:text-white">
        {{ t('addBankAccount.title') }}
      </h3>

      <!-- 表单区域 -->
      <div class="space-y-6">
        <!-- 账户号码 -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {{ t('addBankAccount.accountNumber') }}
          </label>
          <NInput
            v-model:value="formData.accountNumber"
            :placeholder="t('addBankAccount.accountNumberPlaceholder')"
            size="large"
            :status="errors.accountNumber ? 'error' : undefined"
            @blur="validateField('accountNumber')"
          />
          <div v-if="errors.accountNumber" class="mt-1 text-sm text-red-600 dark:text-red-400">
            {{ errors.accountNumber }}
          </div>
        </div>

        <!-- 重新输入账户号码 -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {{ t('addBankAccount.retypeAccountNumber') }}
          </label>
          <NInput
            v-model:value="formData.confirmAccountNumber"
            :placeholder="t('addBankAccount.retypeAccountNumberPlaceholder')"
            size="large"
            :status="errors.confirmAccountNumber ? 'error' : undefined"
            @blur="validateField('confirmAccountNumber')"
          />
          <div
            v-if="errors.confirmAccountNumber"
            class="mt-1 text-sm text-red-600 dark:text-red-400"
          >
            {{ errors.confirmAccountNumber }}
          </div>
        </div>

        <!-- IFSC代码 -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {{ t('addBankAccount.ifscCode') }}
          </label>
          <NInput
            v-model:value="formData.ifscCode"
            :placeholder="t('addBankAccount.ifscCodePlaceholder')"
            size="large"
            :status="errors.ifscCode ? 'error' : undefined"
            @blur="validateField('ifscCode')"
          />
          <div v-if="errors.ifscCode" class="mt-1 text-sm text-red-600 dark:text-red-400">
            {{ errors.ifscCode }}
          </div>
        </div>

        <!-- 姓名 -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {{ t('addBankAccount.name') }}
          </label>
          <NInput v-model:value="formData.name" size="large" disabled />
        </div>

        <!-- 银行证明 -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {{ t('addBankAccount.bankProof') }}
          </label>
          <NUpload
            v-model:file-list="fileList"
            :max="1"
            accept="image/*,.pdf"
            @change="handleFileChange"
          >
            <NUploadDragger>
              <div class="text-4xl mb-2">📄</div>
              <NText class="text-lg">{{ t('addBankAccount.uploadBankProof') }}</NText>
              <NP depth="3" class="mt-2">
                {{ t('addBankAccount.supportedFormats') }}
              </NP>
            </NUploadDragger>
          </NUpload>
        </div>

        <!-- 重要提示 -->
        <NAlert type="warning" :title="t('addBankAccount.important')">
          <ul class="space-y-1">
            <li v-for="note in importantNotes" :key="note">• {{ t(note) }}</li>
          </ul>
        </NAlert>

        <!-- 提交按钮 -->
        <div class="flex gap-4">
          <NButton
            type="primary"
            size="large"
            :loading="loading"
            :disabled="!isFormValid"
            @click="handleSubmit"
            class="flex-1"
          >
            {{ t('addBankAccount.submit') }}
          </NButton>
          <NButton size="large" @click="handleCancel">
            {{ t('common.cancel') }}
          </NButton>
        </div>
      </div>

      <!-- 占位内容 -->
      <div class="text-center py-8 mt-8">
        <div class="text-6xl mb-4">🏦</div>

        <!-- 路由信息显示 -->
        <div class="bg-gray-100 dark:bg-gray-700 rounded-lg p-4 text-left">
          <h5 class="font-semibold mb-2 text-gray-900 dark:text-white">Route Info</h5>
          <div class="text-sm text-gray-600 dark:text-gray-300 space-y-1">
            <div><strong>Path:</strong> {{ $route.path }}</div>
            <div><strong>Name:</strong> {{ $route.name }}</div>
            <div><strong>Original:</strong> /pages/deposit/addcreditcard/addcreditcard</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { NInput, NButton, NUpload, NUploadDragger, NText, NP, NAlert } from 'naive-ui'
import { useI18n } from '@/composables/useI18n'
import { useNaive } from '@/composables/useNaive'
import { isValidAccountNumber, isValidIFSC } from '@/utils/business'
import type { BankAccountFormData } from '@/types/api'

const router = useRouter()
const { t } = useI18n()
const { message } = useNaive()

// 表单数据
const formData = ref<BankAccountFormData>({
  accountNumber: '',
  confirmAccountNumber: '',
  ifscCode: '',
  name: 'Test' // 从用户信息获取
})

// 文件列表
const fileList = ref([])

// 表单验证错误
const errors = ref<Record<string, string>>({})

// 加载状态
const loading = ref(false)

// 重要提示
const importantNotes = ['addBankAccount.importantNotes.0', 'addBankAccount.importantNotes.1']

// 计算属性
const isFormValid = computed(() => {
  return (
    formData.value.accountNumber &&
    formData.value.confirmAccountNumber &&
    formData.value.ifscCode &&
    formData.value.accountNumber === formData.value.confirmAccountNumber &&
    isValidAccountNumber(formData.value.accountNumber) &&
    isValidIFSC(formData.value.ifscCode) &&
    Object.keys(errors.value).length === 0
  )
})

// 方法
function validateField(field: keyof BankAccountFormData) {
  switch (field) {
    case 'accountNumber':
      if (!formData.value.accountNumber) {
        errors.value.accountNumber = t('addBankAccount.errors.accountNumberRequired')
      } else if (!isValidAccountNumber(formData.value.accountNumber)) {
        errors.value.accountNumber = t('addBankAccount.errors.invalidAccountNumber')
      } else {
        delete errors.value.accountNumber
      }
      break

    case 'confirmAccountNumber':
      if (!formData.value.confirmAccountNumber) {
        errors.value.confirmAccountNumber = t('addBankAccount.errors.confirmAccountNumberRequired')
      } else if (formData.value.confirmAccountNumber !== formData.value.accountNumber) {
        errors.value.confirmAccountNumber = t('addBankAccount.errors.accountNumberMismatch')
      } else {
        delete errors.value.confirmAccountNumber
      }
      break

    case 'ifscCode':
      if (!formData.value.ifscCode) {
        errors.value.ifscCode = t('addBankAccount.errors.ifscCodeRequired')
      } else if (!isValidIFSC(formData.value.ifscCode)) {
        errors.value.ifscCode = t('addBankAccount.errors.invalidIfscCode')
      } else {
        delete errors.value.ifscCode
      }
      break
  }
}

function handleFileChange() {
  // 处理文件上传
  console.log('File changed:', fileList.value)
}

async function handleSubmit() {
  // 验证所有字段
  validateField('accountNumber')
  validateField('confirmAccountNumber')
  validateField('ifscCode')

  if (!isFormValid.value) {
    message.error(t('addBankAccount.errors.formInvalid'))
    return
  }

  loading.value = true

  try {
    // TODO: 实现银行账户添加API调用
    // await ApiService.addBankAccount(formData.value)

    // 模拟API调用
    await new Promise((resolve) => setTimeout(resolve, 2000))

    message.success(t('addBankAccount.success.accountAdded'))
    router.back()
  } catch (error) {
    message.error(t('addBankAccount.errors.submitFailed'))
  } finally {
    loading.value = false
  }
}

function handleCancel() {
  router.back()
}
</script>

<style scoped>
.add-bank-account-page {
  @apply w-full;
}
</style>
