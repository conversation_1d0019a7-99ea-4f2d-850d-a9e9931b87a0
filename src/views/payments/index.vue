<template>
  <div class="payments-page">
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
      <h3 class="text-lg font-semibold mb-4 text-gray-900 dark:text-white">
        {{ t('payments.title') }}
      </h3>

      <!-- 银行账户部分 -->
      <div class="mb-8">
        <h4 class="text-md font-medium mb-3 text-gray-900 dark:text-white">
          {{ t('payments.myBankAccounts') }}
        </h4>
        <div
          class="p-4 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg text-center"
        >
          <p class="text-gray-500 dark:text-gray-400">{{ t('payments.verifyNewBankAccount') }}</p>
        </div>
      </div>

      <!-- UPI钱包部分 -->
      <div class="mb-8">
        <h4 class="text-md font-medium mb-3 text-gray-900 dark:text-white">
          {{ t('payments.myWalletsUpiId') }}
        </h4>
        <div class="space-y-3">
          <div
            v-for="wallet in wallets"
            :key="wallet.key"
            class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors cursor-pointer"
            @click="handleWalletClick(wallet)"
          >
            <div class="flex items-center gap-3">
              <span class="text-2xl">{{ wallet.icon }}</span>
              <div>
                <span class="font-medium text-gray-900 dark:text-white block">{{
                  t(wallet.label)
                }}</span>
                <span v-if="wallet.linked" class="text-sm text-gray-500 dark:text-gray-400">
                  {{ wallet.account || 'Linked Account' }}
                </span>
              </div>
            </div>
            <div class="flex items-center gap-2">
              <span
                :class="[
                  'px-3 py-1 rounded-full text-sm font-medium',
                  wallet.linked
                    ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
                    : 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
                ]"
              >
                {{ wallet.linked ? t('payments.linked') : t('payments.link') }}
              </span>
              <div class="text-gray-400">
                <div class="i-carbon-chevron-right text-lg"></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 占位内容 -->
      <div class="text-center py-8">
        <div class="text-6xl mb-4">💳</div>

        <!-- 路由信息显示 -->
        <div class="bg-gray-100 dark:bg-gray-700 rounded-lg p-4 text-left">
          <h5 class="font-semibold mb-2 text-gray-900 dark:text-white">Route Info</h5>
          <div class="text-sm text-gray-600 dark:text-gray-300 space-y-1">
            <div><strong>Path:</strong> {{ $route.path }}</div>
            <div><strong>Name:</strong> {{ $route.name }}</div>
            <div><strong>Original:</strong> /pages/deposit/managepayment/managepayment</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useI18n } from '@/composables/useI18n'
import { useNaive } from '@/composables/useNaive'

const { t } = useI18n()
const { message } = useNaive()

// 钱包配置
const wallets = ref([
  {
    key: 'paytm',
    label: 'payments.walletTypes.paytm',
    icon: '📱',
    linked: true,
    account: 'test@paytm'
  },
  {
    key: 'phonepe',
    label: 'payments.walletTypes.phonepe',
    icon: '💜',
    linked: false,
    account: null
  },
  { key: 'gpay', label: 'payments.walletTypes.gpay', icon: '🟢', linked: false, account: null },
  {
    key: 'otherUpi',
    label: 'payments.walletTypes.otherUpi',
    icon: '💰',
    linked: false,
    account: null
  }
])

// 方法
function handleWalletClick(wallet: any) {
  if (wallet.linked) {
    message.info(`${t(wallet.label)} ${t('payments.alreadyLinked')}`)
  } else {
    message.info(`${t('payments.linkWallet')} ${t(wallet.label)}`)
    // TODO: 实现钱包绑定逻辑
  }
}
</script>

<style scoped>
.payments-page {
  @apply w-full;
}
</style>
