<template>
  <div class="transactions-page">
    <!-- 加载状态 -->
    <div v-if="transactionsStore.loading" class="flex justify-center items-center py-12">
      <NSpin size="large">
        <template #description>{{ t('common.loading') }}</template>
      </NSpin>
    </div>

    <!-- 主要内容 -->
    <div v-else class="space-y-6">
      <!-- 标签页导航 -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h3 class="text-lg font-semibold mb-4 text-gray-900 dark:text-white">
          {{ t('transactions.title') }}
        </h3>

        <div class="flex space-x-1 bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
          <button
            v-for="tab in tabs"
            :key="tab.key"
            :class="[
              'flex-1 py-2 px-4 text-sm font-medium rounded-md transition-colors',
              transactionsStore.activeTab === tab.key
                ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow'
                : 'text-gray-500 dark:text-gray-300 hover:text-gray-700 dark:hover:text-white'
            ]"
            @click="switchTab(tab.key)"
          >
            {{ t(tab.label) }}
          </button>
        </div>
      </div>

      <!-- 交易列表 -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow">
        <!-- 有交易记录时 -->
        <div
          v-if="transactionsStore.hasTransactions"
          class="divide-y divide-gray-200 dark:divide-gray-700"
        >
          <!-- 按日期分组的交易 (deposits, bonus) -->
          <template v-if="isGroupedTransactions">
            <div v-for="group in groupedTransactions" :key="group.date" class="p-6">
              <!-- 日期标题 -->
              <h4 class="text-sm font-semibold text-gray-500 dark:text-gray-400 mb-4">
                {{ formatDate(group.date) }}
              </h4>

              <!-- 该日期的交易列表 -->
              <div class="space-y-3">
                <div
                  v-for="transaction in group.transactions"
                  :key="transaction.id"
                  class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg"
                >
                  <div class="flex-1">
                    <div class="flex items-center gap-3">
                      <div class="text-2xl">
                        {{ getTransactionIcon(transactionsStore.activeTab) }}
                      </div>
                      <div>
                        <h5 class="font-medium text-gray-900 dark:text-white">
                          {{ transaction.title }}
                        </h5>
                        <p class="text-sm text-gray-500 dark:text-gray-400">
                          {{ transaction.memo || transaction.orderid }}
                        </p>
                      </div>
                    </div>
                  </div>

                  <div class="text-right">
                    <div class="font-semibold text-gray-900 dark:text-white">
                      {{ formatCurrency(transaction.coin) }}
                    </div>
                    <div class="flex items-center gap-2 mt-1">
                      <span
                        :class="[
                          'px-2 py-1 rounded-full text-xs font-medium',
                          getStatusClass(transaction.statusInfo.color)
                        ]"
                      >
                        {{ transaction.status_str }}
                      </span>
                      <span class="text-xs text-gray-500 dark:text-gray-400">
                        {{ transaction.time }}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </template>

          <!-- 简单列表的交易 (withdrawals, bet) -->
          <template v-else>
            <div class="p-6">
              <div class="space-y-3">
                <div
                  v-for="transaction in flatTransactions"
                  :key="transaction.id"
                  class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg"
                >
                  <div class="flex-1">
                    <div class="flex items-center gap-3">
                      <div class="text-2xl">
                        {{ getTransactionIcon(transactionsStore.activeTab) }}
                      </div>
                      <div>
                        <h5 class="font-medium text-gray-900 dark:text-white">
                          {{ transaction.title }}
                        </h5>
                        <p class="text-sm text-gray-500 dark:text-gray-400">
                          {{ transaction.memo || transaction.orderid }}
                        </p>
                      </div>
                    </div>
                  </div>

                  <div class="text-right">
                    <div class="font-semibold text-gray-900 dark:text-white">
                      {{ formatCurrency(transaction.coin) }}
                    </div>
                    <div class="flex items-center gap-2 mt-1">
                      <span
                        :class="[
                          'px-2 py-1 rounded-full text-xs font-medium',
                          getStatusClass(transaction.statusInfo.color)
                        ]"
                      >
                        {{ transaction.status_str }}
                      </span>
                      <span class="text-xs text-gray-500 dark:text-gray-400">
                        {{ transaction.time }}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </template>
        </div>

        <!-- 无交易记录时 -->
        <div v-else class="text-center py-12">
          <div class="text-6xl mb-4">{{ getTransactionIcon(transactionsStore.activeTab) }}</div>
          <h4 class="text-xl font-semibold mb-2 text-gray-900 dark:text-white">
            {{ t('transactions.noTransactions') }}
          </h4>
          <p class="text-gray-600 dark:text-gray-300 mb-6">
            {{ getEmptyMessage(transactionsStore.activeTab) }}
          </p>

          <!-- 刷新按钮 -->
          <NButton @click="refreshTransactions">
            {{ t('common.refresh') }}
          </NButton>
        </div>
      </div>

      <!-- 错误提示 -->
      <NAlert
        v-if="transactionsStore.error"
        type="error"
        :title="t('common.error')"
        closable
        @close="transactionsStore.clearError"
      >
        {{ transactionsStore.error }}
      </NAlert>

      <!-- 加载更多按钮 -->
      <div v-if="transactionsStore.hasTransactions" class="text-center">
        <NButton :loading="transactionsStore.loading" @click="loadMoreTransactions">
          {{ t('transactions.loadMore') }}
        </NButton>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, computed } from 'vue'
import { NSpin, NButton, NAlert } from 'naive-ui'
import { useI18n, useCurrency } from '@/composables/useI18n'
import { useTransactionsStore } from '@/stores/transactions'
import { formatDate } from '@/utils/business'
import type { Transaction } from '@/types/api'

const { t } = useI18n()
const { format: formatCurrency } = useCurrency()
const transactionsStore = useTransactionsStore()

// 计算属性来处理不同的数据结构
const isGroupedTransactions = computed(() => {
  const data = transactionsStore.currentTabTransactions
  return !Array.isArray(data) && typeof data === 'object' && Object.keys(data).length > 0
})

const groupedTransactions = computed(() => {
  if (!isGroupedTransactions.value) return []

  const data = transactionsStore.currentTabTransactions as Record<string, Transaction[]>
  return Object.entries(data).map(([date, transactions]) => ({
    date,
    transactions: transactions.map((transaction) => ({
      ...transaction,
      statusInfo: getTransactionStatus(
        transaction.status,
        transactionsStore.activeTab === 'bonus' ? 'bonus' : 'deposit'
      )
    }))
  }))
})

const flatTransactions = computed(() => {
  if (isGroupedTransactions.value) return []

  const data = transactionsStore.currentTabTransactions as Transaction[]
  return data.map((transaction) => ({
    ...transaction,
    statusInfo: getTransactionStatus(
      transaction.status,
      transactionsStore.activeTab === 'bonus' ? 'bonus' : 'deposit'
    )
  }))
})

// 状态信息类型定义
interface StatusInfo {
  text: string
  class: string
  color: string
}

// 从utils导入状态处理函数
function getTransactionStatus(status: number, type: 'deposit' | 'bonus' = 'deposit'): StatusInfo {
  const TRANSACTION_STATUS = {
    DEPOSIT: {
      0: { text: 'In-Process', class: 'status-pending', color: 'warning' },
      1: { text: 'Success', class: 'status-success', color: 'success' },
      2: { text: 'Failed', class: 'status-failed', color: 'error' }
    },
    BONUS: {
      34: { text: 'Transfer to Cash Balance', class: 'status-transfer-out', color: 'info' },
      35: { text: 'Login bonus', class: 'status-bonus', color: 'success' }
    }
  } as const

  const mapping = TRANSACTION_STATUS[type.toUpperCase() as keyof typeof TRANSACTION_STATUS]
  return (
    mapping[status as keyof typeof mapping] || {
      text: 'Unknown',
      class: 'status-unknown',
      color: 'default'
    }
  )
}

// 标签页配置
const tabs = [
  { key: 'deposits', label: 'transactions.tabs.deposits' },
  { key: 'withdrawals', label: 'transactions.tabs.withdrawals' },
  { key: 'bet', label: 'transactions.tabs.bet' },
  { key: 'bonus', label: 'transactions.tabs.bonus' }
]

// 方法
function switchTab(tab: string) {
  transactionsStore.switchTab(tab as 'deposits' | 'withdrawals' | 'bet' | 'bonus')
}

function getTransactionIcon(tab: string): string {
  const icons = {
    deposits: '💰',
    withdrawals: '💸',
    bet: '🎲',
    bonus: '🎁'
  }
  return icons[tab as keyof typeof icons] || '📊'
}

function getEmptyMessage(tab: string): string {
  const messages = {
    deposits: t('transactions.noDeposits'),
    withdrawals: t('transactions.noWithdrawals'),
    bet: t('transactions.noBets'),
    bonus: t('transactions.noBonus')
  }
  return messages[tab as keyof typeof messages] || t('transactions.noTransactions')
}

function getStatusClass(color: string): string {
  const classes = {
    success: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
    error: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
    warning: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
    info: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
    default: 'bg-gray-100 text-gray-800 dark:bg-gray-600 dark:text-gray-300'
  }
  return classes[color as keyof typeof classes] || classes.default
}

async function refreshTransactions() {
  await transactionsStore.refreshTransactions()
}

async function loadMoreTransactions() {
  await transactionsStore.loadMoreTransactions()
}

// 生命周期
onMounted(() => {
  transactionsStore.fetchTransactionHistory()
})
</script>
