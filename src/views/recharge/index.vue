<template>
  <div class="recharge-page">
    <!-- 加载状态 -->
    <div v-if="userStore.loading" class="flex justify-center items-center py-12">
      <NSpin size="large">
        <template #description>{{ t('common.loading') }}</template>
      </NSpin>
    </div>

    <!-- 主要内容 -->
    <div v-else class="space-y-6">
      <!-- 当前余额显示 -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div class="flex justify-between items-center">
          <div>
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-1">
              {{ t('deposit.currentBalance') }}
            </h3>
            <p class="text-sm text-gray-500 dark:text-gray-400">
              {{ t('deposit.allowedEachTime') }}
            </p>
          </div>
          <div class="text-right">
            <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">
              {{ formatCurrency(userStore.currentBalance) }}
            </div>
            <div
              v-if="userStore.bonusBalance > 0"
              class="text-sm text-green-600 dark:text-green-400"
            >
              {{ t('deposit.bonusFixed') }}: {{ formatCurrency(userStore.bonusBalance) }}
            </div>
          </div>
        </div>
      </div>

      <!-- 金额输入区域 -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h4 class="text-md font-semibold text-gray-900 dark:text-white mb-4">
          {{ t('deposit.amount') }}
        </h4>

        <!-- 金额输入框 -->
        <div class="mb-4">
          <NInputNumber
            v-model:value="userStore.selectedAmount"
            :placeholder="t('deposit.amount')"
            :min="selectedChannelLimits?.min || 0"
            :max="selectedChannelLimits?.max || 1000000"
            size="large"
            class="w-full"
            @update:value="handleAmountChange"
          >
            <template #prefix>
              <span class="text-gray-500">{{ currentCurrency }}</span>
            </template>
          </NInputNumber>

          <!-- 限额提示 -->
          <div v-if="selectedChannelLimits" class="mt-2 text-sm text-gray-500 dark:text-gray-400">
            {{ t('deposit.depositMin') }}: {{ formatCurrency(selectedChannelLimits.min) }} &
            {{ t('deposit.depositMax') }}: {{ formatCurrency(selectedChannelLimits.max) }}
          </div>
        </div>

        <!-- 预设金额按钮 -->
        <div class="grid grid-cols-4 gap-3">
          <NButton
            v-for="coin in userStore.presetCoins"
            :key="coin"
            :type="userStore.selectedAmount === coin ? 'primary' : 'default'"
            size="small"
            @click="selectAmount(coin)"
          >
            {{ coin }}
          </NButton>
        </div>
      </div>

      <!-- 支付方式选择 -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h4 class="text-md font-semibold text-gray-900 dark:text-white mb-4">
          {{ t('deposit.paymentMethod') }}
        </h4>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div
            v-for="channel in userStore.paymentChannels"
            :key="channel.id"
            :class="[
              'relative p-4 border-2 rounded-lg cursor-pointer transition-all',
              userStore.selectedChannel?.id === channel.id
                ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'
            ]"
            @click="selectPaymentMethod(channel)"
          >
            <!-- 奖励标签 -->
            <div
              class="absolute -top-2 -right-2 bg-red-500 text-white text-xs px-2 py-1 rounded-full"
            >
              {{ channel.disrate }}
            </div>

            <div class="flex items-center gap-3">
              <img
                v-if="channel.icon"
                :src="channel.icon"
                :alt="channel.title"
                class="w-8 h-8 object-contain"
                @error="handleImageError"
              />
              <div class="flex-1">
                <h5 class="font-medium text-gray-900 dark:text-white">
                  {{ channel.title }}
                </h5>
                <p class="text-sm text-gray-500 dark:text-gray-400">
                  {{ userStore.selectedChannelTitle }}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 计算结果显示 -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div class="grid grid-cols-2 gap-6">
          <div class="text-center">
            <div class="text-sm text-gray-500 dark:text-gray-400 mb-1">
              {{ t('deposit.cashBalance') }}
            </div>
            <div class="text-xl font-bold text-gray-900 dark:text-white">
              {{ formatCurrency(userStore.selectedAmount) }}
            </div>
          </div>
          <div class="text-center">
            <div class="text-sm text-gray-500 dark:text-gray-400 mb-1">
              {{ t('deposit.cashBonus') }}
            </div>
            <div class="text-xl font-bold text-green-600 dark:text-green-400">
              {{ formatCurrency(userStore.calculatedBonus) }}
            </div>
          </div>
        </div>
      </div>

      <!-- 错误提示 -->
      <NAlert
        v-if="userStore.error"
        type="error"
        :title="t('common.error')"
        closable
        @close="userStore.clearError"
      >
        {{ userStore.error }}
      </NAlert>

      <!-- 验证错误提示 -->
      <NAlert
        v-if="validationError"
        type="warning"
        :title="t('common.warning')"
        closable
        @close="validationError = ''"
      >
        {{ validationError }}
      </NAlert>

      <!-- 提交按钮 -->
      <div class="text-center">
        <NButton
          type="primary"
          size="large"
          :loading="userStore.loading"
          :disabled="!canSubmit"
          @click="handleSubmit"
        >
          {{ t('deposit.depositNow') }}
        </NButton>
      </div>

      <!-- 提示信息 -->
      <div
        v-if="userStore.memo"
        class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4"
      >
        <h4 class="font-semibold text-yellow-800 dark:text-yellow-300 mb-2">
          {{ t('deposit.tips') }}
        </h4>
        <div class="text-sm text-yellow-700 dark:text-yellow-400" v-html="userStore.memo"></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import { NInputNumber, NButton, NSpin, NAlert } from 'naive-ui'
import { useI18n, useCurrency } from '@/composables/useI18n'
import { useNaive } from '@/composables/useNaive'
import { useUserStore } from '@/stores/user'
import type { PaymentChannel } from '@/types/api'

const { t } = useI18n()
const { format: formatCurrency, currency: currentCurrency } = useCurrency()
const { message } = useNaive()
const userStore = useUserStore()

// 本地状态
const validationError = ref('')

// 计算属性
const selectedChannelLimits = computed(() => userStore.selectedChannelLimits)

const canSubmit = computed(() => {
  return (
    userStore.selectedAmount > 0 &&
    userStore.selectedChannel &&
    !userStore.loading &&
    userStore.validateDepositAmount(userStore.selectedAmount).valid
  )
})

// 方法
function selectAmount(amount: number) {
  userStore.setSelectedAmount(amount)

  // 清除之前的验证错误
  validationError.value = ''

  // 实时验证
  const validation = userStore.validateDepositAmount(amount)
  if (!validation.valid) {
    validationError.value = validation.message || ''
  }
}

function selectPaymentMethod(channel: PaymentChannel) {
  userStore.selectPaymentChannel(channel)

  // 重新验证当前金额
  const validation = userStore.validateDepositAmount(userStore.selectedAmount)
  validationError.value = validation.valid ? '' : validation.message || ''
}

function handleAmountChange(value: number | null) {
  if (value !== null) {
    userStore.setSelectedAmount(value)

    // 实时验证
    const validation = userStore.validateDepositAmount(value)
    validationError.value = validation.valid ? '' : validation.message || ''
  }
}

function handleImageError(event: Event) {
  const img = event.target as HTMLImageElement
  img.style.display = 'none'
}

async function handleSubmit() {
  const validation = userStore.validateDepositAmount(userStore.selectedAmount)
  if (!validation.valid) {
    message.error(validation.message || t('deposit.errors.invalidAmount'))
    return
  }

  const success = await userStore.submitDepositOrder()
  if (success) {
    message.success(t('deposit.success.orderSubmitted'))
  }
}

// 生命周期
onMounted(() => {
  userStore.fetchUserBalance()
})
</script>
