<template>
  <div class="error-404-page min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
    <div class="text-center">
      <div class="text-9xl mb-4">🔍</div>
      <h1 class="text-6xl font-bold text-gray-900 dark:text-white mb-4">404</h1>
      <h2 class="text-2xl font-semibold text-gray-700 dark:text-gray-300 mb-6">
        {{ t('common.notFound') || 'Page Not Found' }}
      </h2>
      <p class="text-gray-600 dark:text-gray-400 mb-8 max-w-md">
        {{ t('common.notFoundDesc') || 'The page you are looking for might have been removed, had its name changed, or is temporarily unavailable.' }}
      </p>
      
      <div class="space-y-4">
        <button
          @click="goHome"
          class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          {{ t('nav.home') || 'Go Home' }}
        </button>
        
        <div class="text-sm text-gray-500 dark:text-gray-400">
          <p>{{ t('common.or') || 'or' }}</p>
          <button
            @click="goBack"
            class="text-blue-600 dark:text-blue-400 hover:underline"
          >
            {{ t('common.back') || 'Go Back' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { useI18n } from '@/composables/useI18n'
import { ROUTE_PATHS } from '@/router'

const router = useRouter()
const { t } = useI18n()

function goHome() {
  router.push(ROUTE_PATHS.DEPOSIT_ADD)
}

function goBack() {
  router.go(-1)
}
</script>

<style scoped>
.error-404-page {
  @apply w-full;
}
</style>
