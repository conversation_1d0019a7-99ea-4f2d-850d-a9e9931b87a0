<script setup lang="ts">
import { computed } from 'vue'
import { NConfigProvider, darkTheme } from 'naive-ui'
import { useNaive } from './composables/useNaive'

const { theme } = useNaive()

const naiveTheme = computed(() => (theme.value === 'dark' ? darkTheme : null))
</script>

<template>
  <NConfigProvider :theme="naiveTheme">
    <router-view />
  </NConfigProvider>
</template>

<style lang="scss" scoped>
// 使用 Sass 变量
.custom-card {
  background: linear-gradient(135deg, $primary-color, $info-color);
  border-radius: $border-radius-large;
  padding: $space-lg;
  box-shadow: $box-shadow-2;

  &:hover {
    transform: translateY(-2px);
    transition: all 0.3s $cubic-bezier-ease-in-out;
  }
}
</style>
