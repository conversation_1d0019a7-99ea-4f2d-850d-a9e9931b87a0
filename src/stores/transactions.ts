import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import ApiService, { handleApiError } from '@/services/api'
import type { TransactionHistory, Transaction } from '@/types/api'
import { getTransactionStatus } from '@/utils/business'

export const useTransactionsStore = defineStore('transactions', () => {
  // 状态
  const transactionHistory = ref<TransactionHistory | null>(null)
  const activeTab = ref<'deposits' | 'withdrawals' | 'bet' | 'bonus'>('deposits')
  const loading = ref(false)
  const error = ref<string | null>(null)
  const currentPage = ref(1)
  const pageSize = ref(10)
  const customerUrl = ref<string>('')

  // 计算属性
  const depositTransactions = computed(() => {
    if (!transactionHistory.value?.deplist) return {}
    return transactionHistory.value.deplist
  })

  const withdrawalTransactions = computed(() => {
    return transactionHistory.value?.drawlist || []
  })

  const betTransactions = computed(() => {
    return transactionHistory.value?.betlist || []
  })

  const bonusTransactions = computed(() => {
    if (!transactionHistory.value?.bonuslist) return {}
    return transactionHistory.value.bonuslist
  })

  const bankTransactions = computed(() => {
    return transactionHistory.value?.banklist || []
  })

  // 当前标签页的交易数据
  const currentTabTransactions = computed(() => {
    switch (activeTab.value) {
      case 'deposits':
        return depositTransactions.value
      case 'withdrawals':
        return withdrawalTransactions.value
      case 'bet':
        return betTransactions.value
      case 'bonus':
        return bonusTransactions.value
      default:
        return {}
    }
  })

  // 格式化交易数据为列表
  const formattedTransactions = computed(() => {
    const data = currentTabTransactions.value
    
    if (Array.isArray(data)) {
      // 对于数组类型的数据 (withdrawals, bet)
      return data.map(transaction => ({
        ...transaction,
        statusInfo: getTransactionStatus(transaction.status, activeTab.value === 'bonus' ? 'bonus' : 'deposit')
      }))
    } else {
      // 对于按日期分组的数据 (deposits, bonus)
      const result: Array<{ date: string; transactions: Transaction[] }> = []
      
      Object.entries(data).forEach(([date, transactions]) => {
        if (Array.isArray(transactions)) {
          result.push({
            date,
            transactions: transactions.map(transaction => ({
              ...transaction,
              statusInfo: getTransactionStatus(transaction.status, activeTab.value === 'bonus' ? 'bonus' : 'deposit')
            }))
          })
        }
      })
      
      return result
    }
  })

  const hasTransactions = computed(() => {
    const data = currentTabTransactions.value
    if (Array.isArray(data)) {
      return data.length > 0
    } else {
      return Object.keys(data).length > 0
    }
  })

  // Actions
  async function fetchTransactionHistory(page: number = 1, size: number = 10) {
    loading.value = true
    error.value = null
    
    try {
      const data: TransactionHistory = await ApiService.getTransactionHistory(page, size)
      
      transactionHistory.value = data
      customerUrl.value = data.url
      currentPage.value = page
      pageSize.value = size
      
    } catch (err: any) {
      error.value = handleApiError(err)
      console.error('Failed to fetch transaction history:', err)
    } finally {
      loading.value = false
    }
  }

  // 切换标签页
  function switchTab(tab: 'deposits' | 'withdrawals' | 'bet' | 'bonus') {
    activeTab.value = tab
  }

  // 刷新数据
  async function refreshTransactions() {
    await fetchTransactionHistory(currentPage.value, pageSize.value)
  }

  // 加载更多数据
  async function loadMoreTransactions() {
    const nextPage = currentPage.value + 1
    await fetchTransactionHistory(nextPage, pageSize.value)
  }

  // 清除错误
  function clearError() {
    error.value = null
  }

  // 重置状态
  function resetState() {
    transactionHistory.value = null
    activeTab.value = 'deposits'
    loading.value = false
    error.value = null
    currentPage.value = 1
    pageSize.value = 10
    customerUrl.value = ''
  }

  return {
    // 状态
    transactionHistory,
    activeTab,
    loading,
    error,
    currentPage,
    pageSize,
    customerUrl,

    // 计算属性
    depositTransactions,
    withdrawalTransactions,
    betTransactions,
    bonusTransactions,
    bankTransactions,
    currentTabTransactions,
    formattedTransactions,
    hasTransactions,

    // Actions
    fetchTransactionHistory,
    switchTab,
    refreshTransactions,
    loadMoreTransactions,
    clearError,
    resetState
  }
})
