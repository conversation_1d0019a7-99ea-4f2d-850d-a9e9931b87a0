import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import ApiService, { handleApiError } from '@/services/api'
import type {
  WithdrawInfo,
  BankAccountsResponse,
  ValidationResult
} from '@/types/api'

export const useWithdrawStore = defineStore('withdraw', () => {
  // 状态
  const withdrawInfo = ref<WithdrawInfo | null>(null)
  const bankAccounts = ref<any[]>([])
  const selectedAmount = ref<number>(0)
  const loading = ref(false)
  const error = ref<string | null>(null)
  const memo = ref<string>('')
  const customerUrl = ref<string>('')

  // 计算属性
  const withdrawableBalance = computed(() => withdrawInfo.value?.dcoin || 0)
  const minWithdrawAmount = computed(() => withdrawInfo.value?.mincoin || 0)
  const maxWithdrawAmount = computed(() => withdrawInfo.value?.maxcoin || 1000000)
  const hasWithdrawLimit = computed(() => (withdrawInfo.value?.limit || 0) > 0)
  const hasBankAccounts = computed(() => bankAccounts.value.length > 0)

  // 是否可以提现
  const canWithdraw = computed(() => {
    return withdrawableBalance.value > 0 && 
           hasBankAccounts.value && 
           selectedAmount.value > 0 &&
           selectedAmount.value <= withdrawableBalance.value
  })

  // Actions
  async function fetchWithdrawInfo() {
    loading.value = true
    error.value = null
    
    try {
      const data: WithdrawInfo = await ApiService.getWithdrawInfo()
      
      withdrawInfo.value = data
      memo.value = data.memo
      customerUrl.value = data.url
      
    } catch (err: any) {
      error.value = handleApiError(err)
      console.error('Failed to fetch withdraw info:', err)
    } finally {
      loading.value = false
    }
  }

  async function fetchBankAccounts() {
    loading.value = true
    error.value = null
    
    try {
      const data: BankAccountsResponse = await ApiService.getBankAccounts()
      
      bankAccounts.value = data.banks || []
      
    } catch (err: any) {
      error.value = handleApiError(err)
      console.error('Failed to fetch bank accounts:', err)
    } finally {
      loading.value = false
    }
  }

  // 验证提现金额
  function validateWithdrawAmount(amount: number): ValidationResult {
    if (amount <= 0) {
      return {
        valid: false,
        message: 'Please enter withdrawal amount'
      }
    }

    if (amount < minWithdrawAmount.value) {
      return {
        valid: false,
        message: `Minimum withdrawal amount is ${minWithdrawAmount.value}`
      }
    }

    if (amount > maxWithdrawAmount.value) {
      return {
        valid: false,
        message: `Maximum withdrawal amount is ${maxWithdrawAmount.value}`
      }
    }

    if (amount > withdrawableBalance.value) {
      return {
        valid: false,
        message: 'Insufficient balance'
      }
    }

    return { valid: true }
  }

  // 设置提现金额
  function setWithdrawAmount(amount: number) {
    selectedAmount.value = amount
  }

  // 提交提现申请
  async function submitWithdrawRequest(): Promise<boolean> {
    if (!hasBankAccounts.value) {
      error.value = 'Please add a bank account first'
      return false
    }

    const validation = validateWithdrawAmount(selectedAmount.value)
    if (!validation.valid) {
      error.value = validation.message || 'Invalid amount'
      return false
    }

    loading.value = true
    error.value = null

    try {
      // TODO: 实现提现API调用
      // const result = await ApiService.submitWithdrawRequest({
      //   amount: selectedAmount.value,
      //   bank_id: selectedBankAccount.value.id
      // })
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // 提交成功后刷新信息
      await fetchWithdrawInfo()
      
      return true
    } catch (err: any) {
      error.value = handleApiError(err)
      console.error('Failed to submit withdraw request:', err)
      return false
    } finally {
      loading.value = false
    }
  }

  // 清除错误
  function clearError() {
    error.value = null
  }

  // 重置状态
  function resetState() {
    withdrawInfo.value = null
    bankAccounts.value = []
    selectedAmount.value = 0
    loading.value = false
    error.value = null
    memo.value = ''
    customerUrl.value = ''
  }

  return {
    // 状态
    withdrawInfo,
    bankAccounts,
    selectedAmount,
    loading,
    error,
    memo,
    customerUrl,

    // 计算属性
    withdrawableBalance,
    minWithdrawAmount,
    maxWithdrawAmount,
    hasWithdrawLimit,
    hasBankAccounts,
    canWithdraw,

    // Actions
    fetchWithdrawInfo,
    fetchBankAccounts,
    validateWithdrawAmount,
    setWithdrawAmount,
    submitWithdrawRequest,
    clearError,
    resetState
  }
})
