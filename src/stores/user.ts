import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import ApiService, { handleApiError } from '@/services/api'
import type {
  UserBalance,
  PaymentChannel,
  UserBalanceResponse,
  DepositOrderData,
  ValidationResult
} from '@/types/api'

export const useUserStore = defineStore('user', () => {
  // 状态
  const balance = ref<UserBalance | null>(null)
  const paymentChannels = ref<PaymentChannel[]>([])
  const presetCoins = ref<number[]>([])
  const selectedChannel = ref<PaymentChannel | null>(null)
  const selectedAmount = ref<number>(300)
  const loading = ref(false)
  const error = ref<string | null>(null)
  const memo = ref<string>('')
  const customerUrl = ref<string>('')

  // 计算属性
  const currentBalance = computed(() => balance.value?.coin || 0)
  const withdrawableBalance = computed(() => balance.value?.dcoin || 0)
  const totalBalance = computed(() => balance.value?.totalcoin || 0)
  const bonusBalance = computed(() => balance.value?.bonus || 0)
  const isKycVerified = computed(() => balance.value?.kyc === 1)

  // 选中支付方式的限额
  const selectedChannelLimits = computed(() => {
    if (!selectedChannel.value) return null
    return {
      min: selectedChannel.value.mincoin,
      max: selectedChannel.value.maxcoin
    }
  })

  // 计算奖励金额
  const calculatedBonus = computed(() => {
    if (!selectedChannel.value || selectedAmount.value <= 0) return 0

    if (selectedChannel.value.discoin > 0) {
      // 固定奖励 (如AliPay的500)
      return selectedChannel.value.discoin
    } else {
      // 百分比奖励 (如UPI的3%)
      const rate =
        typeof selectedChannel.value.pages[0]?.rate === 'string'
          ? parseFloat(selectedChannel.value.pages[0].rate)
          : selectedChannel.value.pages[0]?.rate || 0
      return selectedAmount.value * rate
    }
  })

  // 获取支付渠道显示名称
  const selectedChannelTitle = computed(() => {
    return selectedChannel.value?.pages[0]?.title || selectedChannel.value?.title || ''
  })

  // Actions
  async function fetchUserBalance(coin: number = 300) {
    loading.value = true
    error.value = null

    try {
      const data: UserBalanceResponse = await ApiService.getUserBalance(coin)

      balance.value = data.user
      paymentChannels.value = data.channels
      presetCoins.value = data.coins
      memo.value = data.memo
      customerUrl.value = data.url

      // 如果没有选中的支付方式，默认选择第一个
      if (!selectedChannel.value && data.channels.length > 0) {
        selectedChannel.value = data.channels[0]
      }
    } catch (err: any) {
      error.value = handleApiError(err)
      console.error('Failed to fetch user balance:', err)
    } finally {
      loading.value = false
    }
  }

  // 选择支付方式
  function selectPaymentChannel(channel: PaymentChannel) {
    selectedChannel.value = channel
    // 重新获取配置以更新奖励计算
    if (selectedAmount.value > 0) {
      fetchUserBalance(selectedAmount.value)
    }
  }

  // 设置选中金额
  function setSelectedAmount(amount: number) {
    selectedAmount.value = amount
    // 重新获取配置以更新奖励计算
    fetchUserBalance(amount)
  }

  // 验证充值金额
  function validateDepositAmount(amount: number): ValidationResult {
    if (!selectedChannel.value) {
      return {
        valid: false,
        message: 'Please select a payment method'
      }
    }

    if (amount < selectedChannel.value.mincoin) {
      return {
        valid: false,
        message: `Deposit amount cannot less than ${selectedChannel.value.mincoin}`
      }
    }

    if (amount > selectedChannel.value.maxcoin) {
      return {
        valid: false,
        message: `Deposit amount cannot exceed ${selectedChannel.value.maxcoin}`
      }
    }

    return { valid: true }
  }

  // 提交充值订单
  async function submitDepositOrder(): Promise<boolean> {
    if (!selectedChannel.value) {
      error.value = 'Please select a payment method'
      return false
    }

    const validation = validateDepositAmount(selectedAmount.value)
    if (!validation.valid) {
      error.value = validation.message || 'Invalid amount'
      return false
    }

    loading.value = true
    error.value = null

    try {
      const orderData: DepositOrderData = {
        amount: selectedAmount.value,
        channel_id: selectedChannel.value.id,
        page_id: selectedChannel.value.pages[0]?.id || selectedChannel.value.id
      }

      await ApiService.submitDepositOrder(orderData)
      await fetchUserBalance(selectedAmount.value)

      return true
    } catch (err: any) {
      error.value = handleApiError(err)
      console.error('Failed to submit deposit order:', err)
      return false
    } finally {
      loading.value = false
    }
  }

  // 清除错误
  function clearError() {
    error.value = null
  }

  // 重置状态
  function resetState() {
    balance.value = null
    paymentChannels.value = []
    presetCoins.value = []
    selectedChannel.value = null
    selectedAmount.value = 300
    loading.value = false
    error.value = null
    memo.value = ''
    customerUrl.value = ''
  }

  return {
    // 状态
    balance,
    paymentChannels,
    presetCoins,
    selectedChannel,
    selectedAmount,
    loading,
    error,
    memo,
    customerUrl,

    // 计算属性
    currentBalance,
    withdrawableBalance,
    totalBalance,
    bonusBalance,
    isKycVerified,
    selectedChannelLimits,
    calculatedBonus,
    selectedChannelTitle,

    // Actions
    fetchUserBalance,
    selectPaymentChannel,
    setSelectedAmount,
    validateDepositAmount,
    submitDepositOrder,
    clearError,
    resetState
  }
})
