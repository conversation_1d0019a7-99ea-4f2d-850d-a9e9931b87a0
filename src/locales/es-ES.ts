export default {
  // Común
  common: {
    currency: '€',
    loading: 'Cargando...',
    submit: 'Enviar',
    cancel: 'Can<PERSON><PERSON>',
    confirm: 'Confirmar',
    back: 'Atr<PERSON>',
    next: 'Siguient<PERSON>',
    save: '<PERSON>ar',
    edit: 'Editar',
    delete: 'Eliminar',
    add: 'Aña<PERSON>',
    copy: 'Copiar',
    link: '<PERSON><PERSON><PERSON>',
    ok: 'OK',
    retry: 'Reintentar',
    refresh: 'Actualizar'
  },

  // Navegación
  nav: {
    deposit: 'Depósito',
    withdraw: 'Retirar',
    transactions: 'Transacciones',
    payments: 'Pagos',
    home: 'Inicio'
  },

  // Página de depósito
  deposit: {
    title: 'Depósito',
    currentBalance: 'Saldo Actual',
    amount: 'Cantidad',
    paymentMethod: 'Método de Pago',
    paymentChannel: 'Canal de Pago',
    cashBalance: 'Saldo en Efectivo',
    cashBonus: 'Bono en Efectivo',
    depositNow: 'DEPOSITAR AHORA',
    depositMin: 'Dep<PERSON><PERSON>',
    depositMax: 'Depósito Máx',
    allowedEachTime: 'Permitido Cada Vez',
    tips: 'Consejos',
    presetAmounts: 'Cantidades Predefinidas',
    bonusFixed: 'Bono Fijo',
    bonusPercent: 'Bono Porcentual',
    
    // Mensajes de error
    errors: {
      amountTooLow: 'La cantidad del depósito no puede ser menor que {min}',
      amountTooHigh: 'La cantidad del depósito no puede exceder {max}',
      paymentNotAvailable: '¡Este método de pago no está disponible ahora, por favor elija otros métodos de pago!',
      networkError: 'Error de conexión de red, por favor verifique su conexión e intente de nuevo',
      serverError: 'Error del servidor, por favor intente más tarde',
      invalidAmount: 'Por favor ingrese una cantidad válida'
    },

    // Mensajes de éxito
    success: {
      orderSubmitted: 'Pedido enviado exitosamente',
      redirecting: 'Redirigiendo a la página de pago...'
    }
  },

  // Página de retiro
  withdraw: {
    title: 'Retirar',
    withdrawableBalance: 'Saldo Retirable',
    amount: 'Cantidad',
    withdrawNow: 'RETIRAR AHORA',
    withdrawMin: 'Retiro Mín',
    withdrawMax: 'Retiro Máx',
    addNewBankAccount: 'Añadir Nueva Cuenta Bancaria',
    addBankAccountDesc: 'Añadir una nueva cuenta bancaria para retiros',
    noBankAccount: 'No hay cuenta bancaria disponible',
    
    // Mensajes de error
    errors: {
      insufficientBalance: 'Saldo insuficiente',
      noBankAccountError: 'Por favor añada una cuenta bancaria primero',
      connectionFailed: 'No se puede conectar al servidor',
      amountRequired: 'Por favor ingrese la cantidad de retiro'
    }
  },

  // Página de transacciones
  transactions: {
    title: 'Mis Transacciones',
    tabs: {
      deposits: 'Depósitos',
      withdrawals: 'Retiros',
      bet: 'Apuesta',
      bonus: 'Bono'
    },
    status: {
      success: 'Éxito',
      inProcess: 'En Proceso',
      failed: 'Fallido',
      refund: 'Reembolso',
      win: 'Ganancia',
      loss: 'Pérdida',
      transferIn: 'Transferencia Entrante',
      transferOut: 'Transferencia Saliente'
    },
    depositUsuallyCredited: 'Los depósitos generalmente se acreditan en minutos',
    withdrawUsuallyProcessed: 'Los retiros generalmente se procesan en minutos',
    allBettingRecords: 'Todos los registros de apuestas se muestran aquí',
    allBonusRecords: 'Todos los registros de bonos se muestran aquí',
    noTransactions: 'No has realizado ninguna transacción hasta ahora',
    needHelp: '¿Necesitas ayuda con este pedido? Haz clic para contactarnos',
    orderIdCopied: 'ID del pedido copiado',
    
    // Tipos de bono
    bonusTypes: {
      loginBonus: 'Bono de inicio de sesión',
      transferToCashBalance: 'Transferir al saldo en efectivo',
      signInBonus: 'Bono de registro diario'
    }
  },

  // Página de pagos
  payments: {
    title: 'Gestionar Pagos',
    myBankAccounts: 'Mis Cuentas Bancarias',
    myWalletsUpiId: 'Mis Carteras UPI ID',
    verifyNewBankAccount: 'Verificar Nueva Cuenta Bancaria',
    walletTypes: {
      paytm: 'Paytm',
      phonepe: 'PhonePe',
      gpay: 'GPay',
      otherUpi: 'Otro UPI ID'
    },
    linked: 'Enlazado',
    notLinked: 'No Enlazado'
  },

  // Página de añadir cuenta bancaria
  addBankAccount: {
    title: 'Añadir Nueva Cuenta Bancaria',
    accountNumber: 'Número de Cuenta',
    accountNumberPlaceholder: 'Ingrese su número de cuenta bancaria',
    retypeAccountNumber: 'Vuelva a escribir el Número de Cuenta',
    retypeAccountNumberPlaceholder: 'Confirme su número de cuenta bancaria',
    ifscCode: 'Código IFSC',
    ifscCodePlaceholder: 'Ingrese el código IFSC bancario de 11 dígitos',
    name: 'Nombre',
    bankProof: 'Comprobante Bancario',
    submitDetails: 'Enviar Detalles',
    
    // Notas importantes
    important: 'Importante',
    importantNotes: [
      'Por favor verifique sus detalles cuidadosamente antes de enviar el documento',
      'La cuenta bancaria no se puede cambiar una vez añadida'
    ],
    
    // Mensajes de error
    errors: {
      accountNumberRequired: 'Por favor ingrese el número de cuenta',
      accountNumberMismatch: 'Los números de cuenta no coinciden',
      ifscCodeRequired: 'Por favor ingrese el código IFSC',
      ifscCodeInvalid: 'Formato de código IFSC inválido',
      bankProofRequired: 'Por favor suba el comprobante bancario',
      submitFailed: 'Envío fallido, por favor intente de nuevo'
    }
  },

  // Estado y notificaciones
  status: {
    connecting: 'Conectando...',
    connected: 'Conectado',
    disconnected: 'Desconectado',
    processing: 'Procesando...',
    completed: 'Completado',
    failed: 'Fallido',
    pending: 'Pendiente',
    verified: 'Verificado',
    unverified: 'No Verificado'
  },

  // Formato de tiempo
  time: {
    am: 'AM',
    pm: 'PM',
    today: 'Hoy',
    yesterday: 'Ayer',
    thisWeek: 'Esta Semana',
    thisMonth: 'Este Mes'
  },

  // Soporte y ayuda
  support: {
    needHelp: '¿Necesitas Ayuda?',
    contactUs: 'Contáctanos',
    customerService: 'Servicio al Cliente',
    onlineSupport: 'Soporte en Línea',
    helpCenter: 'Centro de Ayuda'
  }
}
