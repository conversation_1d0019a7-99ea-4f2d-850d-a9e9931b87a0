export default {
  // Common
  common: {
    currency: '$',
    loading: 'Loading...',
    submit: 'Submit',
    cancel: 'Cancel',
    confirm: 'Confirm',
    back: 'Back',
    next: 'Next',
    save: 'Save',
    edit: 'Edit',
    delete: 'Delete',
    add: 'Add',
    copy: 'Copy',
    link: 'Link',
    ok: 'OK',
    retry: 'Retry',
    refresh: 'Refresh',
    error: 'Error',
    success: 'Success',
    warning: 'Warning',
    info: 'Info'
  },

  // Navigation
  nav: {
    deposit: 'Deposit',
    withdraw: 'Withdraw',
    transactions: 'Transactions',
    payments: 'Payments',
    home: 'Home'
  },

  // Deposit page
  deposit: {
    title: 'Deposit',
    currentBalance: 'Current Balance',
    amount: 'Amount',
    paymentMethod: 'Payment Method',
    paymentChannel: 'Payment Channel',
    cashBalance: 'Cash Balance',
    cashBonus: 'Cash Bonus',
    depositNow: 'DEPOSIT NOW',
    depositMin: 'Min Deposit',
    depositMax: 'Max Deposit',
    allowedEachTime: 'Allowed Each Time',
    tips: 'Tips',
    presetAmounts: 'Preset Amounts',
    bonusFixed: 'Fixed Bonus',
    bonusPercent: 'Percentage Bonus',

    // Error messages
    errors: {
      amountTooLow: 'Deposit amount cannot less than {min}',
      amountTooHigh: 'Deposit amount cannot exceed {max}',
      paymentNotAvailable:
        'This payment method is not available now, Please choose other payment methods to pay!',
      networkError: 'Network connection failed, please check your connection and try again',
      serverError: 'Server error, please try again later',
      invalidAmount: 'Please enter a valid amount'
    },

    // Success messages
    success: {
      orderSubmitted: 'Order submitted successfully',
      redirecting: 'Redirecting to payment page...'
    }
  },

  // Withdraw page
  withdraw: {
    title: 'Withdraw',
    withdrawableBalance: 'Withdrawable Balance',
    amount: 'Amount',
    withdrawNow: 'WITHDRAW NOW',
    withdrawMin: 'Min Withdraw',
    withdrawMax: 'Max Withdraw',
    addNewBankAccount: 'Add New Bank Account',
    addBankAccountDesc: 'Add a new bank account for withdrawals',
    noBankAccount: 'No bank account available',

    // Error messages
    errors: {
      insufficientBalance: 'Insufficient balance',
      noBankAccountError: 'Please add a bank account first',
      connectionFailed: 'Unable to connect to server',
      amountRequired: 'Please enter withdrawal amount'
    }
  },

  // Transactions page
  transactions: {
    title: 'My Transactions',
    tabs: {
      deposits: 'Deposits',
      withdrawals: 'Withdrawals',
      bet: 'Bet',
      bonus: 'Bonus'
    },
    status: {
      success: 'Success',
      inProcess: 'In-Process',
      failed: 'Failed',
      refund: 'Refund',
      win: 'Win',
      loss: 'Loss',
      transferIn: 'Transfer In',
      transferOut: 'Transfer Out'
    },
    depositUsuallyCredited: 'Deposits are usually credited within minutes',
    withdrawUsuallyProcessed: 'Withdrawals are usually processed within minutes',
    allBettingRecords: 'All betting records are shown here',
    allBonusRecords: 'All bonus records are shown here',
    noTransactions: "You've not done any transactions till now",
    noDeposits: 'No deposit records',
    noWithdrawals: 'No withdrawal records',
    noBets: 'No betting records',
    noBonus: 'No bonus records',
    loadMore: 'Load More',
    needHelp: 'Need help with this order? Click to contact us',
    orderIdCopied: 'Order ID copied',

    // Bonus types
    bonusTypes: {
      loginBonus: 'Login bonus',
      transferToCashBalance: 'Transfer to Cash Balance',
      signInBonus: 'Sign-in bonus'
    }
  },

  // Payments page
  payments: {
    title: 'Manage Payments',
    myBankAccounts: 'My Bank Accounts',
    myWalletsUpiId: 'My Wallets UPI ID',
    verifyNewBankAccount: 'Verify New Bank Account',
    walletTypes: {
      paytm: 'Paytm',
      phonepe: 'PhonePe',
      gpay: 'GPay',
      otherUpi: 'Other UPI ID'
    },
    linked: 'Linked',
    notLinked: 'Not Linked'
  },

  // Add bank account page
  addBankAccount: {
    title: 'Add New Bank Account',
    accountNumber: 'Account Number',
    accountNumberPlaceholder: 'Enter your bank account number',
    retypeAccountNumber: 'Re-type Account Number',
    retypeAccountNumberPlaceholder: 'Confirm your bank account number',
    ifscCode: 'IFSC Code',
    ifscCodePlaceholder: 'Enter 11-digit bank IFSC code',
    name: 'Name',
    bankProof: 'Bank Proof',
    submitDetails: 'Submit Details',

    // Important notes
    important: 'Important',
    importantNotes: [
      'Please check your details carefully before submitting the document',
      'Bank account cannot be changed once added'
    ],

    // Error messages
    errors: {
      accountNumberRequired: 'Please enter account number',
      accountNumberMismatch: 'Account numbers do not match',
      ifscCodeRequired: 'Please enter IFSC code',
      ifscCodeInvalid: 'Invalid IFSC code format',
      bankProofRequired: 'Please upload bank proof',
      submitFailed: 'Submit failed, please try again'
    }
  },

  // Status and notifications
  status: {
    connecting: 'Connecting...',
    connected: 'Connected',
    disconnected: 'Disconnected',
    processing: 'Processing...',
    completed: 'Completed',
    failed: 'Failed',
    pending: 'Pending',
    verified: 'Verified',
    unverified: 'Unverified'
  },

  // Time format
  time: {
    am: 'AM',
    pm: 'PM',
    today: 'Today',
    yesterday: 'Yesterday',
    thisWeek: 'This Week',
    thisMonth: 'This Month'
  },

  // Support and help
  support: {
    needHelp: 'Need Help?',
    contactUs: 'Contact Us',
    customerService: 'Customer Service',
    onlineSupport: 'Online Support',
    helpCenter: 'Help Center'
  }
}
