export default {
  // Comum
  common: {
    currency: 'R$',
    loading: 'Carregando...',
    submit: 'Enviar',
    cancel: '<PERSON><PERSON><PERSON>',
    confirm: 'Confirmar',
    back: '<PERSON>tar',
    next: 'Próximo',
    save: '<PERSON><PERSON>',
    edit: 'Editar',
    delete: 'Excluir',
    add: 'Adicionar',
    copy: 'Copiar',
    link: 'Vincular',
    ok: 'OK',
    retry: 'Tentar Novamente',
    refresh: 'Atualizar'
  },

  // Navegação
  nav: {
    deposit: 'Depósito',
    withdraw: 'Saque',
    transactions: 'Transações',
    payments: 'Pagamentos',
    home: 'Início'
  },

  // Página de depósito
  deposit: {
    title: 'Depósito',
    currentBalance: 'Saldo Atual',
    amount: 'Valor',
    paymentMethod: 'Método de Pagamento',
    paymentChannel: 'Canal de Pagamento',
    cashBalance: 'Saldo em Dinheiro',
    cashBonus: 'Bônus em Dinheiro',
    depositNow: 'DEPOSITAR AGORA',
    depositMin: 'Dep<PERSON><PERSON>',
    depositMax: 'Depósito <PERSON>á<PERSON>',
    allowedEachTime: 'Permitido a Cada Vez',
    tips: 'Dicas',
    presetAmounts: 'Valores Predefinidos',
    bonusFixed: 'Bônus Fixo',
    bonusPercent: 'Bônus Percentual',
    
    // Mensagens de erro
    errors: {
      amountTooLow: 'O valor do depósito não pode ser menor que {min}',
      amountTooHigh: 'O valor do depósito não pode exceder {max}',
      paymentNotAvailable: 'Este método de pagamento não está disponível agora, por favor escolha outros métodos de pagamento!',
      networkError: 'Falha na conexão de rede, por favor verifique sua conexão e tente novamente',
      serverError: 'Erro do servidor, por favor tente mais tarde',
      invalidAmount: 'Por favor insira um valor válido'
    },

    // Mensagens de sucesso
    success: {
      orderSubmitted: 'Pedido enviado com sucesso',
      redirecting: 'Redirecionando para a página de pagamento...'
    }
  },

  // Página de saque
  withdraw: {
    title: 'Saque',
    withdrawableBalance: 'Saldo Disponível para Saque',
    amount: 'Valor',
    withdrawNow: 'SACAR AGORA',
    withdrawMin: 'Saque Mín',
    withdrawMax: 'Saque Máx',
    addNewBankAccount: 'Adicionar Nova Conta Bancária',
    addBankAccountDesc: 'Adicionar uma nova conta bancária para saques',
    noBankAccount: 'Nenhuma conta bancária disponível',
    
    // Mensagens de erro
    errors: {
      insufficientBalance: 'Saldo insuficiente',
      noBankAccountError: 'Por favor adicione uma conta bancária primeiro',
      connectionFailed: 'Não foi possível conectar ao servidor',
      amountRequired: 'Por favor insira o valor do saque'
    }
  },

  // Página de transações
  transactions: {
    title: 'Minhas Transações',
    tabs: {
      deposits: 'Depósitos',
      withdrawals: 'Saques',
      bet: 'Aposta',
      bonus: 'Bônus'
    },
    status: {
      success: 'Sucesso',
      inProcess: 'Em Processamento',
      failed: 'Falhou',
      refund: 'Reembolso',
      win: 'Ganho',
      loss: 'Perda',
      transferIn: 'Transferência Recebida',
      transferOut: 'Transferência Enviada'
    },
    depositUsuallyCredited: 'Depósitos geralmente são creditados em minutos',
    withdrawUsuallyProcessed: 'Saques geralmente são processados em minutos',
    allBettingRecords: 'Todos os registros de apostas são mostrados aqui',
    allBonusRecords: 'Todos os registros de bônus são mostrados aqui',
    noTransactions: 'Você não fez nenhuma transação até agora',
    needHelp: 'Precisa de ajuda com este pedido? Clique para nos contatar',
    orderIdCopied: 'ID do pedido copiado',
    
    // Tipos de bônus
    bonusTypes: {
      loginBonus: 'Bônus de login',
      transferToCashBalance: 'Transferir para saldo em dinheiro',
      signInBonus: 'Bônus de check-in diário'
    }
  },

  // Página de pagamentos
  payments: {
    title: 'Gerenciar Pagamentos',
    myBankAccounts: 'Minhas Contas Bancárias',
    myWalletsUpiId: 'Minhas Carteiras UPI ID',
    verifyNewBankAccount: 'Verificar Nova Conta Bancária',
    walletTypes: {
      paytm: 'Paytm',
      phonepe: 'PhonePe',
      gpay: 'GPay',
      otherUpi: 'Outro UPI ID'
    },
    linked: 'Vinculado',
    notLinked: 'Não Vinculado'
  },

  // Página de adicionar conta bancária
  addBankAccount: {
    title: 'Adicionar Nova Conta Bancária',
    accountNumber: 'Número da Conta',
    accountNumberPlaceholder: 'Digite o número da sua conta bancária',
    retypeAccountNumber: 'Digite Novamente o Número da Conta',
    retypeAccountNumberPlaceholder: 'Confirme o número da sua conta bancária',
    ifscCode: 'Código IFSC',
    ifscCodePlaceholder: 'Digite o código IFSC bancário de 11 dígitos',
    name: 'Nome',
    bankProof: 'Comprovante Bancário',
    submitDetails: 'Enviar Detalhes',
    
    // Notas importantes
    important: 'Importante',
    importantNotes: [
      'Por favor verifique seus detalhes cuidadosamente antes de enviar o documento',
      'A conta bancária não pode ser alterada uma vez adicionada'
    ],
    
    // Mensagens de erro
    errors: {
      accountNumberRequired: 'Por favor digite o número da conta',
      accountNumberMismatch: 'Os números da conta não coincidem',
      ifscCodeRequired: 'Por favor digite o código IFSC',
      ifscCodeInvalid: 'Formato de código IFSC inválido',
      bankProofRequired: 'Por favor envie o comprovante bancário',
      submitFailed: 'Envio falhou, por favor tente novamente'
    }
  },

  // Status e notificações
  status: {
    connecting: 'Conectando...',
    connected: 'Conectado',
    disconnected: 'Desconectado',
    processing: 'Processando...',
    completed: 'Concluído',
    failed: 'Falhou',
    pending: 'Pendente',
    verified: 'Verificado',
    unverified: 'Não Verificado'
  },

  // Formato de tempo
  time: {
    am: 'AM',
    pm: 'PM',
    today: 'Hoje',
    yesterday: 'Ontem',
    thisWeek: 'Esta Semana',
    thisMonth: 'Este Mês'
  },

  // Suporte e ajuda
  support: {
    needHelp: 'Precisa de Ajuda?',
    contactUs: 'Entre em Contato',
    customerService: 'Atendimento ao Cliente',
    onlineSupport: 'Suporte Online',
    helpCenter: 'Central de Ajuda'
  }
}
