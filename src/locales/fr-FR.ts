export default {
  // Commun
  common: {
    currency: '€',
    loading: 'Chargement...',
    submit: 'Soumettre',
    cancel: 'Annule<PERSON>',
    confirm: 'Confirmer',
    back: 'Retour',
    next: 'Suivant',
    save: 'Enregistrer',
    edit: 'Modifier',
    delete: 'Supprimer',
    add: 'Ajouter',
    copy: 'Copier',
    link: 'Lier',
    ok: 'OK',
    retry: 'R<PERSON>sayer',
    refresh: 'Actualiser'
  },

  // Navigation
  nav: {
    deposit: 'Dépôt',
    withdraw: 'Retrait',
    transactions: 'Transactions',
    payments: 'Paiements',
    home: 'Accueil'
  },

  // Page de dépôt
  deposit: {
    title: 'Dépôt',
    currentBalance: 'Solde Actuel',
    amount: 'Montant',
    paymentMethod: 'Méthode de Paiement',
    paymentChannel: 'Canal de Paiement',
    cashBalance: 'Solde Espèces',
    cashBonus: 'Bonus Espèces',
    depositNow: 'DÉPOSER MAINTENANT',
    depositMin: '<PERSON>é<PERSON><PERSON>t Min',
    depositMax: 'Dépôt Max',
    allowedEachTime: 'Autorisé à Chaque Fois',
    tips: 'Conseils',
    presetAmounts: 'Montants Prédéfinis',
    bonusFixed: 'Bonus Fixe',
    bonusPercent: 'Bonus en Pourcentage',
    
    // Messages d'erreur
    errors: {
      amountTooLow: 'Le montant du dépôt ne peut pas être inférieur à {min}',
      amountTooHigh: 'Le montant du dépôt ne peut pas dépasser {max}',
      paymentNotAvailable: 'Cette méthode de paiement n\'est pas disponible maintenant, veuillez choisir d\'autres méthodes de paiement !',
      networkError: 'Échec de la connexion réseau, veuillez vérifier votre connexion et réessayer',
      serverError: 'Erreur serveur, veuillez réessayer plus tard',
      invalidAmount: 'Veuillez entrer un montant valide'
    },

    // Messages de succès
    success: {
      orderSubmitted: 'Commande soumise avec succès',
      redirecting: 'Redirection vers la page de paiement...'
    }
  },

  // Page de retrait
  withdraw: {
    title: 'Retrait',
    withdrawableBalance: 'Solde Retirable',
    amount: 'Montant',
    withdrawNow: 'RETIRER MAINTENANT',
    withdrawMin: 'Retrait Min',
    withdrawMax: 'Retrait Max',
    addNewBankAccount: 'Ajouter un Nouveau Compte Bancaire',
    addBankAccountDesc: 'Ajouter un nouveau compte bancaire pour les retraits',
    noBankAccount: 'Aucun compte bancaire disponible',
    
    // Messages d'erreur
    errors: {
      insufficientBalance: 'Solde insuffisant',
      noBankAccountError: 'Veuillez d\'abord ajouter un compte bancaire',
      connectionFailed: 'Impossible de se connecter au serveur',
      amountRequired: 'Veuillez entrer le montant du retrait'
    }
  },

  // Page des transactions
  transactions: {
    title: 'Mes Transactions',
    tabs: {
      deposits: 'Dépôts',
      withdrawals: 'Retraits',
      bet: 'Pari',
      bonus: 'Bonus'
    },
    status: {
      success: 'Succès',
      inProcess: 'En Cours',
      failed: 'Échoué',
      refund: 'Remboursement',
      win: 'Gagné',
      loss: 'Perdu',
      transferIn: 'Transfert Entrant',
      transferOut: 'Transfert Sortant'
    },
    depositUsuallyCredited: 'Les dépôts sont généralement crédités en quelques minutes',
    withdrawUsuallyProcessed: 'Les retraits sont généralement traités en quelques minutes',
    allBettingRecords: 'Tous les enregistrements de paris sont affichés ici',
    allBonusRecords: 'Tous les enregistrements de bonus sont affichés ici',
    noTransactions: 'Vous n\'avez effectué aucune transaction jusqu\'à présent',
    needHelp: 'Besoin d\'aide avec cette commande ? Cliquez pour nous contacter',
    orderIdCopied: 'ID de commande copié',
    
    // Types de bonus
    bonusTypes: {
      loginBonus: 'Bonus de connexion',
      transferToCashBalance: 'Transfert vers le solde espèces',
      signInBonus: 'Bonus de connexion quotidienne'
    }
  },

  // Page des paiements
  payments: {
    title: 'Gérer les Paiements',
    myBankAccounts: 'Mes Comptes Bancaires',
    myWalletsUpiId: 'Mes Portefeuilles UPI ID',
    verifyNewBankAccount: 'Vérifier un Nouveau Compte Bancaire',
    walletTypes: {
      paytm: 'Paytm',
      phonepe: 'PhonePe',
      gpay: 'GPay',
      otherUpi: 'Autre UPI ID'
    },
    linked: 'Lié',
    notLinked: 'Non Lié'
  },

  // Page d'ajout de compte bancaire
  addBankAccount: {
    title: 'Ajouter un Nouveau Compte Bancaire',
    accountNumber: 'Numéro de Compte',
    accountNumberPlaceholder: 'Entrez votre numéro de compte bancaire',
    retypeAccountNumber: 'Retapez le Numéro de Compte',
    retypeAccountNumberPlaceholder: 'Confirmez votre numéro de compte bancaire',
    ifscCode: 'Code IFSC',
    ifscCodePlaceholder: 'Entrez le code IFSC bancaire à 11 chiffres',
    name: 'Nom',
    bankProof: 'Preuve Bancaire',
    submitDetails: 'Soumettre les Détails',
    
    // Notes importantes
    important: 'Important',
    importantNotes: [
      'Veuillez vérifier attentivement vos détails avant de soumettre le document',
      'Le compte bancaire ne peut pas être modifié une fois ajouté'
    ],
    
    // Messages d'erreur
    errors: {
      accountNumberRequired: 'Veuillez entrer le numéro de compte',
      accountNumberMismatch: 'Les numéros de compte ne correspondent pas',
      ifscCodeRequired: 'Veuillez entrer le code IFSC',
      ifscCodeInvalid: 'Format de code IFSC invalide',
      bankProofRequired: 'Veuillez télécharger la preuve bancaire',
      submitFailed: 'Échec de la soumission, veuillez réessayer'
    }
  },

  // Statut et notifications
  status: {
    connecting: 'Connexion...',
    connected: 'Connecté',
    disconnected: 'Déconnecté',
    processing: 'Traitement...',
    completed: 'Terminé',
    failed: 'Échoué',
    pending: 'En Attente',
    verified: 'Vérifié',
    unverified: 'Non Vérifié'
  },

  // Format de temps
  time: {
    am: 'AM',
    pm: 'PM',
    today: 'Aujourd\'hui',
    yesterday: 'Hier',
    thisWeek: 'Cette Semaine',
    thisMonth: 'Ce Mois'
  },

  // Support et aide
  support: {
    needHelp: 'Besoin d\'Aide ?',
    contactUs: 'Nous Contacter',
    customerService: 'Service Client',
    onlineSupport: 'Support en Ligne',
    helpCenter: 'Centre d\'Aide'
  }
}
