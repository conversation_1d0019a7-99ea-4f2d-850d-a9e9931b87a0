import axios, { type AxiosInstance, type AxiosResponse } from 'axios'
import type {
  ApiResponse,
  UserBalanceResponse,
  TransactionHistory,
  WithdrawInfo,
  BankAccountsResponse,
  DepositOrderData,
  OrderResponse
} from '@/types/api'

// API基础URL
const API_BASE_URL = 'http://service.haiwailaba.cyou'

// 创建axios实例
const apiClient: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
    Accept: 'application/json'
  }
})

// 请求拦截器
apiClient.interceptors.request.use(
  (config) => {
    console.log(
      'API Request:',
      config.method?.toUpperCase(),
      config.url,
      config.params || config.data
    )
    return config
  },
  (error) => {
    console.error('Request Error:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
apiClient.interceptors.response.use(
  (response: AxiosResponse<ApiResponse>) => {
    console.log('API Response:', response.config.url, response.data)

    // 检查业务状态码
    if (response.data.code !== 0) {
      const error = new Error(response.data.msg || 'API Error')
      ;(error as any).code = response.data.code
      ;(error as any).response = response
      throw error
    }

    return response.data.data
  },
  (error) => {
    console.error('Response Error:', error)

    // 处理网络错误
    if (!error.response) {
      error.message = 'Network Error: Unable to connect to server'
    } else {
      // 处理HTTP错误
      const status = error.response.status
      switch (status) {
        case 400:
          error.message = 'Bad Request: Invalid parameters'
          break
        case 401:
          error.message = 'Unauthorized: Please login first'
          break
        case 403:
          error.message = 'Forbidden: Access denied'
          break
        case 404:
          error.message = 'Not Found: API endpoint not found'
          break
        case 500:
          error.message = 'Internal Server Error: Please try again later'
          break
        default:
          error.message = `HTTP Error ${status}: ${error.response.statusText}`
      }
    }

    return Promise.reject(error)
  }
)

// API服务类
export class ApiService {
  /**
   * 获取用户余额和支付配置
   */
  static async getUserBalance(coin: number = 300): Promise<UserBalanceResponse> {
    return apiClient.get('/user/balance', {
      params: {
        cat: 2,
        coin,
        paypop: 0
      }
    })
  }

  /**
   * 提交充值订单
   */
  static async submitDepositOrder(orderData: DepositOrderData): Promise<OrderResponse> {
    return apiClient.post('/order/pay', orderData)
  }

  /**
   * 获取交易历史
   */
  static async getTransactionHistory(
    page: number = 1,
    size: number = 10
  ): Promise<TransactionHistory> {
    return apiClient.get('/user/history', {
      params: {
        num: page,
        size
      }
    })
  }

  /**
   * 获取提现信息
   */
  static async getWithdrawInfo(): Promise<WithdrawInfo> {
    return apiClient.get('/draw/index', {
      params: {
        drawpop: 0
      }
    })
  }

  /**
   * 获取银行账户信息
   */
  static async getBankAccounts(split?: boolean): Promise<BankAccountsResponse> {
    const params = split ? { split: 1 } : {}
    return apiClient.get('/user/banks', { params })
  }

  /**
   * 添加银行账户
   */
  static async addBankAccount(accountData: any): Promise<any> {
    return apiClient.post('/user/banks', accountData)
  }

  /**
   * 绑定UPI钱包
   */
  static async bindUPIWallet(walletData: any): Promise<any> {
    return apiClient.post('/user/upi', walletData)
  }
}

// 导出默认实例
export default ApiService

// 导出axios实例供其他地方使用
export { apiClient }

// 工具函数：处理API错误
export function handleApiError(error: any): string {
  if (error.code) {
    // 业务错误
    return error.message || 'Operation failed'
  } else if (error.response) {
    // HTTP错误
    return error.message || 'Server error'
  } else {
    // 网络错误
    return 'Network connection failed'
  }
}

// 工具函数：重试API请求
export async function retryApiCall<T>(
  apiCall: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> {
  let lastError: any

  for (let i = 0; i < maxRetries; i++) {
    try {
      return await apiCall()
    } catch (error) {
      lastError = error

      // 如果是最后一次重试，直接抛出错误
      if (i === maxRetries - 1) {
        throw error
      }

      // 等待一段时间后重试
      await new Promise((resolve) => setTimeout(resolve, delay * (i + 1)))
    }
  }

  throw lastError
}
