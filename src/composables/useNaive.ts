import { ref, computed } from 'vue'
import { createDiscreteApi, darkTheme, lightTheme, type ConfigProviderProps } from 'naive-ui'

const themeRef = ref<'light' | 'dark'>('light')

const configProviderPropsRef = computed<ConfigProviderProps>(() => ({
  theme: themeRef.value === 'light' ? lightTheme : darkTheme
}))

const { message, notification, dialog, loadingBar } = createDiscreteApi(
  ['message', 'notification', 'dialog', 'loadingBar'],
  {
    configProviderProps: configProviderPropsRef
  }
)

export function useNaive() {
  return {
    message,
    notification,
    dialog,
    loadingBar,
    theme: themeRef,
    toggleTheme: () => {
      themeRef.value = themeRef.value === 'light' ? 'dark' : 'light'
    }
  }
}

// 全局可用的API
export { message, notification, dialog, loadingBar }
