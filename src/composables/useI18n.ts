import { computed } from 'vue'
import { useI18n as useVueI18n } from 'vue-i18n'
import { 
  SUPPORTED_LOCALES, 
  setLocale, 
  getCurrentLocaleConfig, 
  getCurrentCurrency,
  formatCurrency as formatCurrencyUtil,
  type SupportedLocale 
} from '@/plugins/i18n'

export function useI18n() {
  const { t, locale, n, d } = useVueI18n()

  // 当前语言配置
  const currentLocaleConfig = computed(() => getCurrentLocaleConfig())
  
  // 当前货币符号
  const currentCurrency = computed(() => getCurrentCurrency())
  
  // 是否为RTL语言
  const isRTL = computed(() => currentLocaleConfig.value?.rtl || false)

  // 切换语言
  function switchLocale(newLocale: SupportedLocale) {
    setLocale(newLocale)
  }

  // 格式化货币
  function formatCurrency(amount: number | string, targetLocale?: SupportedLocale) {
    return formatCurrencyUtil(amount, targetLocale)
  }

  // 格式化数字
  function formatNumber(value: number, format: string = 'decimal') {
    return n(value, format)
  }

  // 格式化日期
  function formatDate(date: Date | string | number, format: string = 'short') {
    const dateObj = typeof date === 'string' || typeof date === 'number' ? new Date(date) : date
    return d(dateObj, format)
  }

  // 获取翻译文本（支持插值）
  function translate(key: string, values?: Record<string, any>) {
    return t(key, values as Record<string, unknown>)
  }

  // 获取复数形式翻译
  function translatePlural(key: string, count: number, values?: Record<string, any>) {
    return t(key, { count, ...values }, count)
  }

  // 检查是否存在翻译键
  function hasTranslation(key: string) {
    return t(key) !== key
  }

  // 获取语言选项列表
  function getLocaleOptions() {
    return SUPPORTED_LOCALES.map(locale => ({
      value: locale.code,
      label: locale.name,
      flag: locale.flag,
      currency: locale.currency
    }))
  }

  // 根据浏览器语言获取最佳匹配语言
  function getBestMatchLocale() {
    const browserLang = navigator.language.toLowerCase()
    
    // 精确匹配
    const exactMatch = SUPPORTED_LOCALES.find(
      locale => locale.code.toLowerCase() === browserLang
    )
    if (exactMatch) return exactMatch.code

    // 语言代码匹配（忽略地区）
    const langCode = browserLang.split('-')[0]
    const langMatch = SUPPORTED_LOCALES.find(
      locale => locale.code.toLowerCase().startsWith(langCode)
    )
    if (langMatch) return langMatch.code

    // 返回默认语言
    return 'en-US' as SupportedLocale
  }

  return {
    // Vue I18n 原生方法
    t: translate,
    locale,
    n: formatNumber,
    d: formatDate,
    
    // 扩展方法
    switchLocale,
    formatCurrency,
    formatNumber,
    formatDate,
    translate,
    translatePlural,
    hasTranslation,
    getLocaleOptions,
    getBestMatchLocale,
    
    // 计算属性
    currentLocaleConfig,
    currentCurrency,
    isRTL,
    supportedLocales: SUPPORTED_LOCALES
  }
}

// 货币格式化工具函数（可在模板中直接使用）
export function useCurrency() {
  const { currentCurrency, formatCurrency } = useI18n()
  
  return {
    currency: currentCurrency,
    format: formatCurrency
  }
}

// 日期格式化工具函数
export function useDateTime() {
  const { formatDate, translate } = useI18n()
  
  // 格式化相对时间
  function formatRelativeTime(date: Date | string | number) {
    const now = new Date()
    const targetDate = typeof date === 'string' || typeof date === 'number' ? new Date(date) : date
    const diffMs = now.getTime() - targetDate.getTime()
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))
    
    if (diffDays === 0) {
      return translate('time.today')
    } else if (diffDays === 1) {
      return translate('time.yesterday')
    } else if (diffDays < 7) {
      return translate('time.thisWeek')
    } else if (diffDays < 30) {
      return translate('time.thisMonth')
    } else {
      return formatDate(targetDate, 'short')
    }
  }
  
  // 格式化时间（12小时制）
  function formatTime12Hour(date: Date | string | number) {
    const targetDate = typeof date === 'string' || typeof date === 'number' ? new Date(date) : date
    const hours = targetDate.getHours()
    const minutes = targetDate.getMinutes()
    const ampm = hours >= 12 ? translate('time.pm') : translate('time.am')
    const displayHours = hours % 12 || 12
    
    return `${displayHours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')} ${ampm}`
  }
  
  return {
    formatDate,
    formatRelativeTime,
    formatTime12Hour
  }
}

// 表单验证消息国际化
export function useValidationMessages() {
  const { translate } = useI18n()
  
  return {
    required: (field: string) => translate('validation.required', { field }),
    min: (field: string, min: number) => translate('validation.min', { field, min }),
    max: (field: string, max: number) => translate('validation.max', { field, max }),
    email: (field: string) => translate('validation.email', { field }),
    pattern: (field: string) => translate('validation.pattern', { field }),
    mismatch: (field: string) => translate('validation.mismatch', { field })
  }
}
