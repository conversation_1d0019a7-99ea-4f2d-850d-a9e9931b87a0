<template>
  <div class="language-switcher">
    <NDropdown
      :options="localeOptions"
      :value="currentLocale"
      @select="handleLocaleChange"
      trigger="click"
      placement="bottom-end"
    >
      <NButton quaternary circle>
        <template #icon>
          <div class="flex items-center gap-2">
            <span class="text-lg">{{ currentFlag }}</span>
            <span class="text-sm font-medium">{{ currentName }}</span>
            <div class="i-carbon-chevron-down text-sm"></div>
          </div>
        </template>
      </NButton>
    </NDropdown>
  </div>
</template>

<script setup lang="ts">
import { computed, h } from 'vue'
import { NDropdown, NButton } from 'naive-ui'
import { useI18n } from '@/composables/useI18n'
import type { SupportedLocale } from '@/plugins/i18n'

const { locale, switchLocale, currentLocaleConfig, supportedLocales } = useI18n()

// 当前语言代码
const currentLocale = computed(() => locale.value as SupportedLocale)

// 当前语言显示信息
const currentFlag = computed(() => currentLocaleConfig.value?.flag || '🌐')
const currentName = computed(() => currentLocaleConfig.value?.name || 'Language')

// 语言选项列表
const localeOptions = computed(() =>
  supportedLocales.map((locale) => ({
    key: locale.code,
    label: () =>
      h('div', { class: 'flex items-center gap-3 py-1' }, [
        h('span', { class: 'text-lg' }, locale.flag),
        h('div', { class: 'flex flex-col' }, [
          h('span', { class: 'font-medium' }, locale.name),
          h('span', { class: 'text-xs text-gray-500' }, `${locale.currency} • ${locale.code}`)
        ])
      ]),
    value: locale.code
  }))
)

// 处理语言切换
function handleLocaleChange(value: SupportedLocale) {
  switchLocale(value)

  // 可选：显示切换成功提示
  // message.success(`Language switched to ${supportedLocales.find(l => l.code === value)?.name}`)
}
</script>

<style scoped>
.language-switcher {
  @apply inline-block;
}

.language-switcher :deep(.n-button) {
  @apply min-w-20;
}

.language-switcher :deep(.n-dropdown-option) {
  @apply py-2;
}
</style>
