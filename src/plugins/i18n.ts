import { createI18n } from 'vue-i18n'
import zhCN from '@/locales/zh-CN'
import enUS from '@/locales/en-US'
import frFR from '@/locales/fr-FR'
import esES from '@/locales/es-ES'
import ptBR from '@/locales/pt-BR'

// 支持的语言列表
export const SUPPORTED_LOCALES = [
  {
    code: 'zh-CN',
    name: '中文',
    flag: '🇨🇳',
    currency: '¥',
    rtl: false
  },
  {
    code: 'en-US',
    name: 'English',
    flag: '🇺🇸',
    currency: '$',
    rtl: false
  },
  {
    code: 'fr-FR',
    name: 'Français',
    flag: '🇫🇷',
    currency: '€',
    rtl: false
  },
  {
    code: 'es-ES',
    name: '<PERSON>spañol',
    flag: '🇪🇸',
    currency: '€',
    rtl: false
  },
  {
    code: 'pt-BR',
    name: 'Português',
    flag: '🇧🇷',
    currency: 'R$',
    rtl: false
  }
] as const

export type SupportedLocale = typeof SUPPORTED_LOCALES[number]['code']

// 默认语言
export const DEFAULT_LOCALE: SupportedLocale = 'en-US'

// 从localStorage获取保存的语言，如果没有则使用浏览器语言或默认语言
function getInitialLocale(): SupportedLocale {
  // 优先使用localStorage中保存的语言
  const savedLocale = localStorage.getItem('locale') as SupportedLocale
  if (savedLocale && SUPPORTED_LOCALES.some(locale => locale.code === savedLocale)) {
    return savedLocale
  }

  // 其次使用浏览器语言
  const browserLocale = navigator.language
  const matchedLocale = SUPPORTED_LOCALES.find(locale => 
    browserLocale.startsWith(locale.code.split('-')[0])
  )
  
  return matchedLocale?.code || DEFAULT_LOCALE
}

// 创建i18n实例
export const i18n = createI18n({
  legacy: false, // 使用Composition API模式
  locale: getInitialLocale(),
  fallbackLocale: DEFAULT_LOCALE,
  globalInjection: true, // 全局注入$t等方法
  messages: {
    'zh-CN': zhCN,
    'en-US': enUS,
    'fr-FR': frFR,
    'es-ES': esES,
    'pt-BR': ptBR
  },
  // 数字格式化
  numberFormats: {
    'zh-CN': {
      currency: {
        style: 'currency',
        currency: 'CNY',
        notation: 'standard'
      },
      decimal: {
        style: 'decimal',
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      }
    },
    'en-US': {
      currency: {
        style: 'currency',
        currency: 'USD',
        notation: 'standard'
      },
      decimal: {
        style: 'decimal',
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      }
    },
    'fr-FR': {
      currency: {
        style: 'currency',
        currency: 'EUR',
        notation: 'standard'
      },
      decimal: {
        style: 'decimal',
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      }
    },
    'es-ES': {
      currency: {
        style: 'currency',
        currency: 'EUR',
        notation: 'standard'
      },
      decimal: {
        style: 'decimal',
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      }
    },
    'pt-BR': {
      currency: {
        style: 'currency',
        currency: 'BRL',
        notation: 'standard'
      },
      decimal: {
        style: 'decimal',
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      }
    }
  },
  // 日期时间格式化
  datetimeFormats: {
    'zh-CN': {
      short: {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      },
      long: {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        weekday: 'short',
        hour: 'numeric',
        minute: 'numeric'
      }
    },
    'en-US': {
      short: {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      },
      long: {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        weekday: 'short',
        hour: 'numeric',
        minute: 'numeric'
      }
    },
    'fr-FR': {
      short: {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      },
      long: {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        weekday: 'short',
        hour: 'numeric',
        minute: 'numeric'
      }
    },
    'es-ES': {
      short: {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      },
      long: {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        weekday: 'short',
        hour: 'numeric',
        minute: 'numeric'
      }
    },
    'pt-BR': {
      short: {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      },
      long: {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        weekday: 'short',
        hour: 'numeric',
        minute: 'numeric'
      }
    }
  }
})

// 语言切换函数
export function setLocale(locale: SupportedLocale) {
  i18n.global.locale.value = locale
  localStorage.setItem('locale', locale)
  
  // 更新HTML lang属性
  document.documentElement.lang = locale
  
  // 更新页面方向（如果支持RTL）
  const localeConfig = SUPPORTED_LOCALES.find(l => l.code === locale)
  if (localeConfig) {
    document.documentElement.dir = localeConfig.rtl ? 'rtl' : 'ltr'
  }
}

// 获取当前语言配置
export function getCurrentLocaleConfig() {
  const currentLocale = i18n.global.locale.value as SupportedLocale
  return SUPPORTED_LOCALES.find(locale => locale.code === currentLocale)
}

// 获取当前货币符号
export function getCurrentCurrency() {
  const config = getCurrentLocaleConfig()
  return config?.currency || '$'
}

// 格式化货币
export function formatCurrency(amount: number | string, locale?: SupportedLocale) {
  const targetLocale = locale || i18n.global.locale.value as SupportedLocale
  const config = SUPPORTED_LOCALES.find(l => l.code === targetLocale)
  const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount
  
  if (isNaN(numAmount)) return `${config?.currency || '$'}0.00`
  
  return `${config?.currency || '$'}${numAmount.toFixed(2)}`
}

export default i18n
