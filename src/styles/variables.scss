// 颜色变量
$primary-color: #18a058;
$primary-color-hover: #36ad6a;
$primary-color-pressed: #0c7a43;
$primary-color-suppl: #36ad6a;

$info-color: #2080f0;
$info-color-hover: #4098fc;
$info-color-pressed: #1060c9;
$info-color-suppl: #4098fc;

$success-color: #18a058;
$success-color-hover: #36ad6a;
$success-color-pressed: #0c7a43;
$success-color-suppl: #36ad6a;

$warning-color: #f0a020;
$warning-color-hover: #fcb040;
$warning-color-pressed: #c97c10;
$warning-color-suppl: #fcb040;

$error-color: #d03050;
$error-color-hover: #de576d;
$error-color-pressed: #ab1f3f;
$error-color-suppl: #de576d;

// 字体大小
$font-size-tiny: 12px;
$font-size-small: 14px;
$font-size-medium: 16px;
$font-size-large: 18px;
$font-size-huge: 20px;

// 间距
$space-xs: 4px;
$space-sm: 8px;
$space-md: 16px;
$space-lg: 24px;
$space-xl: 32px;
$space-2xl: 48px;

// 圆角
$border-radius-small: 3px;
$border-radius-medium: 6px;
$border-radius-large: 10px;

// 阴影
$box-shadow-1: 0 1px 2px -2px rgba(0, 0, 0, 0.08), 0 3px 6px 0 rgba(0, 0, 0, 0.06), 0 5px 12px 4px rgba(0, 0, 0, 0.04);
$box-shadow-2: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
$box-shadow-3: 0 6px 16px -9px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 12px 48px 16px rgba(0, 0, 0, 0.03);

// 过渡动画
$cubic-bezier-ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
$cubic-bezier-ease-out: cubic-bezier(0.0, 0, 0.2, 1);
$cubic-bezier-ease-in: cubic-bezier(0.4, 0, 1, 1);

// Z-index
$z-index-base: 0;
$z-index-auto: auto;
$z-index-popper: 2000;
$z-index-modal: 3000;
$z-index-message: 4000;
