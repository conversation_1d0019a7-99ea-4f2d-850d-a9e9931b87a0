// 全局样式重置
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  line-height: 1.15;
  -webkit-text-size-adjust: 100%;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen', 'Ubuntu',
    'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// 滚动条样式
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

// 通用工具类
.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-start {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.flex-end {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-ellipsis-2 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  line-clamp: 2;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.text-ellipsis-3 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  line-clamp: 3;
  -webkit-line-clamp: 3;
  overflow: hidden;
}

// 自定义按钮样式
.btn {
  @apply px-4 py-2 rounded-md font-medium transition-all duration-200;
  @apply bg-gray-100 text-gray-700 hover:bg-gray-200 hover:shadow-sm;
  @apply dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600;
  @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50;
}

.btn-primary {
  @apply px-4 py-2 rounded-md font-medium transition-all duration-200;
  @apply bg-blue-600 text-white hover:bg-blue-700 hover:shadow-md;
  @apply dark:bg-blue-500 dark:hover:bg-blue-600;
  @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50;
  @apply disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-blue-600;
}

.btn-secondary {
  @apply px-4 py-2 rounded-md font-medium transition-all duration-200;
  @apply bg-gray-600 text-white hover:bg-gray-700 hover:shadow-md;
  @apply dark:bg-gray-500 dark:hover:bg-gray-400;
  @apply focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-opacity-50;
}

.icon-btn {
  @apply p-2 rounded-full transition-all duration-200;
  @apply bg-gray-100 text-gray-600 hover:bg-gray-200 hover:shadow-sm;
  @apply dark:bg-gray-700 dark:text-gray-400 dark:hover:bg-gray-600;
  @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50;
}

// 状态样式
.status-pending {
  @apply bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300;
}

.status-success {
  @apply bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300;
}

.status-failed {
  @apply bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300;
}

.status-transfer-out {
  @apply bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300;
}

.status-bonus {
  @apply bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300;
}

.status-unknown {
  @apply bg-gray-100 text-gray-800 dark:bg-gray-600 dark:text-gray-300;
}

// 卡片样式
.card {
  @apply bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700;
  @apply transition-shadow duration-200 hover:shadow-md;
}

.card-interactive {
  @apply cursor-pointer hover:shadow-lg hover:border-gray-300 dark:hover:border-gray-600;
  @apply transform hover:-translate-y-0.5 transition-all duration-200;
}

// 动画类
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 响应式断点
@media (max-width: 768px) {
  .mobile-hidden {
    display: none !important;
  }
}

@media (min-width: 769px) {
  .desktop-hidden {
    display: none !important;
  }
}
